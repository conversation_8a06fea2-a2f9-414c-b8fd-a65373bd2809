/* dnsmasq is Copyright (c) 2000-2021 <PERSON>

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; version 2 dated June, 1991, or
   (at your option) version 3 dated 29 June, 2007.
 
   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.
     
   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

#include "dnsmasq.h"

#ifdef ZXIC_ONELINK_TEST
// 添加cfg_get_item函数声明
extern int cfg_get_item(const char *name, char *value, int len);
static void cleanup_authenticated_mac_rules(const char *mac);
#endif

static struct frec *get_new_frec(time_t now, struct server *serv, int force);
static struct frec *lookup_frec(unsigned short id, int fd, void *hash, int *firstp, int *lastp);
static struct frec *lookup_frec_by_query(void *hash, unsigned int flags, unsigned int flagmask);

static unsigned short get_id(void);
static void free_frec(struct frec *f);
static void query_full(time_t now, char *domain);

static void return_reply(time_t now, struct frec *forward, struct dns_header *header, ssize_t n, int status);

/* Send a UDP packet with its source address set as "source" 
   unless nowild is true, when we just send it with the kernel default */
int send_from(int fd, int nowild, char *packet, size_t len, 
	      union mysockaddr *to, union all_addr *source,
	      unsigned int iface)
{
  struct msghdr msg;
  struct iovec iov[1]; 
  union {
    struct cmsghdr align; /* this ensures alignment */
#if defined(HAVE_LINUX_NETWORK)
    char control[CMSG_SPACE(sizeof(struct in_pktinfo))];
#elif defined(IP_SENDSRCADDR)
    char control[CMSG_SPACE(sizeof(struct in_addr))];
#endif
    char control6[CMSG_SPACE(sizeof(struct in6_pktinfo))];
  } control_u;
  
  iov[0].iov_base = packet;
  iov[0].iov_len = len;

  msg.msg_control = NULL;
  msg.msg_controllen = 0;
  msg.msg_flags = 0;
  msg.msg_name = to;
  msg.msg_namelen = sa_len(to);
  msg.msg_iov = iov;
  msg.msg_iovlen = 1;
  
  if (!nowild)
    {
      struct cmsghdr *cmptr;
      msg.msg_control = &control_u;
      msg.msg_controllen = sizeof(control_u);
      cmptr = CMSG_FIRSTHDR(&msg);

      if (to->sa.sa_family == AF_INET)
	{
#if defined(HAVE_LINUX_NETWORK)
	  struct in_pktinfo p;
	  p.ipi_ifindex = 0;
	  p.ipi_spec_dst = source->addr4;
	  msg.msg_controllen = CMSG_SPACE(sizeof(struct in_pktinfo));
	  memcpy(CMSG_DATA(cmptr), &p, sizeof(p));
	  cmptr->cmsg_len = CMSG_LEN(sizeof(struct in_pktinfo));
	  cmptr->cmsg_level = IPPROTO_IP;
	  cmptr->cmsg_type = IP_PKTINFO;
#elif defined(IP_SENDSRCADDR)
	  msg.msg_controllen = CMSG_SPACE(sizeof(struct in_addr));
	  memcpy(CMSG_DATA(cmptr), &(source->addr4), sizeof(source->addr4));
	  cmptr->cmsg_len = CMSG_LEN(sizeof(struct in_addr));
	  cmptr->cmsg_level = IPPROTO_IP;
	  cmptr->cmsg_type = IP_SENDSRCADDR;
#endif
	}
      else
	{
	  struct in6_pktinfo p;
	  p.ipi6_ifindex = iface; /* Need iface for IPv6 to handle link-local addrs */
	  p.ipi6_addr = source->addr6;
	  msg.msg_controllen = CMSG_SPACE(sizeof(struct in6_pktinfo));
	  memcpy(CMSG_DATA(cmptr), &p, sizeof(p));
	  cmptr->cmsg_len = CMSG_LEN(sizeof(struct in6_pktinfo));
	  cmptr->cmsg_type = daemon->v6pktinfo;
	  cmptr->cmsg_level = IPPROTO_IPV6;
	}
    }
  
  while (retry_send(sendmsg(fd, &msg, 0)));

  if (errno != 0)
    {
#ifdef HAVE_LINUX_NETWORK
      /* If interface is still in DAD, EINVAL results - ignore that. */
      if (errno != EINVAL)
	my_syslog(LOG_ERR, _("failed to send packet: %s"), strerror(errno));
#endif
      return 0;
    }
  
  return 1;
}
          
#ifdef HAVE_CONNTRACK
static void set_outgoing_mark(struct frec *forward, int fd)
{
  /* Copy connection mark of incoming query to outgoing connection. */
  unsigned int mark;
  if (get_incoming_mark(&forward->frec_src.source, &forward->frec_src.dest, 0, &mark))
    setsockopt(fd, SOL_SOCKET, SO_MARK, &mark, sizeof(unsigned int));
}
#endif

static void log_query_mysockaddr(unsigned int flags, char *name, union mysockaddr *addr, char *arg)
{
  if (addr->sa.sa_family == AF_INET)
    log_query(flags | F_IPV4, name, (union all_addr *)&addr->in.sin_addr, arg);
  else
    log_query(flags | F_IPV6, name, (union all_addr *)&addr->in6.sin6_addr, arg);
}

static void server_send(struct server *server, int fd,
			const void *header, size_t plen, int flags)
{
  while (retry_send(sendto(fd, header, plen, flags,
			   &server->addr.sa,
			   sa_len(&server->addr))));
}

#ifdef HAVE_DNSSEC
static void server_send_log(struct server *server, int fd,
			const void *header, size_t plen, int dumpflags,
			unsigned int logflags, char *name, char *arg)
{
#ifdef HAVE_DUMPFILE
	  dump_packet(dumpflags, (void *)header, (size_t)plen, NULL, &server->addr);
#endif
	  log_query_mysockaddr(logflags, name, &server->addr, arg);
	  server_send(server, fd, header, plen, 0);
}
#endif

static int domain_no_rebind(char *domain)
{
  struct server *serv;
  int dlen = (int)strlen(domain);
  
  for (serv = daemon->no_rebind; serv; serv = serv->next)
    if (dlen >= serv->domain_len && strcmp(serv->domain, &domain[dlen - serv->domain_len]) == 0)
      return 1;

  return 0;
}

static int forward_query(int udpfd, union mysockaddr *udpaddr,
			 union all_addr *dst_addr, unsigned int dst_iface,
			 struct dns_header *header, size_t plen,  char *limit, time_t now, 
			 struct frec *forward, int ad_reqd, int do_bit)
{
  unsigned int flags = 0;
  unsigned int fwd_flags = 0;
  int is_dnssec = forward && (forward->flags & (FREC_DNSKEY_QUERY | FREC_DS_QUERY));
  struct server *master;
  unsigned int gotname = extract_request(header, plen, daemon->namebuff, NULL);
  void *hash = hash_questions(header, plen, daemon->namebuff);
  unsigned char *oph = find_pseudoheader(header, plen, NULL, NULL, NULL, NULL);
  int old_src = 0;
  int first, last, start = 0;
  int subnet, cacheable, forwarded = 0;
  size_t edns0_len;
  unsigned char *pheader;
  int ede = EDE_UNSET;
  (void)do_bit;
  
  if (header->hb4 & HB4_CD)
    fwd_flags |= FREC_CHECKING_DISABLED;
  if (ad_reqd)
    fwd_flags |= FREC_AD_QUESTION;
  if (oph)
    fwd_flags |= FREC_HAS_PHEADER;
#ifdef HAVE_DNSSEC
  if (do_bit)
    fwd_flags |= FREC_DO_QUESTION;
#endif
  
  /* Check for retry on existing query.
     FREC_DNSKEY and FREC_DS_QUERY are never set in flags, so the test below 
     ensures that no frec created for internal DNSSEC query can be returned here.
     
     Similarly FREC_NO_CACHE is never set in flags, so a query which is
     contigent on a particular source address EDNS0 option will never be matched. */
  if (forward)
    old_src = 1;
  else if ((forward = lookup_frec_by_query(hash, fwd_flags,
					   FREC_CHECKING_DISABLED | FREC_AD_QUESTION | FREC_DO_QUESTION |
					   FREC_HAS_PHEADER | FREC_DNSKEY_QUERY | FREC_DS_QUERY | FREC_NO_CACHE)))
    {
      struct frec_src *src;
      
      for (src = &forward->frec_src; src; src = src->next)
	if (src->orig_id == ntohs(header->id) && 
	    sockaddr_isequal(&src->source, udpaddr))
	  break;
      
      if (src)
	old_src = 1;
      else
	{
	  /* Existing query, but from new source, just add this 
	     client to the list that will get the reply.*/
	  
	  /* Note whine_malloc() zeros memory. */
	  if (!daemon->free_frec_src &&
	      daemon->frec_src_count < daemon->ftabsize &&
	      (daemon->free_frec_src = whine_malloc(sizeof(struct frec_src))))
	    {
	      daemon->frec_src_count++;
	      daemon->free_frec_src->next = NULL;
	    }
	  
	  /* If we've been spammed with many duplicates, return REFUSED. */
	  if (!daemon->free_frec_src)
	    {
	      query_full(now, NULL);
	      goto reply;
	    }
	  
	  src = daemon->free_frec_src;
	  daemon->free_frec_src = src->next;
	  src->next = forward->frec_src.next;
	  forward->frec_src.next = src;
	  src->orig_id = ntohs(header->id);
	  src->source = *udpaddr;
	  src->dest = *dst_addr;
	  src->log_id = daemon->log_id;
	  src->iface = dst_iface;
	  src->fd = udpfd;

	  /* closely spaced identical queries cannot be a try and a retry, so
	     it's safe to wait for the reply from the first without
	     forwarding the second. */
	  if (difftime(now, forward->time) < 2)
	    return 0;
	}
    }

  /* new query */
  if (!forward)
    {
      if (lookup_domain(daemon->namebuff, gotname, &first, &last))
	flags = is_local_answer(now, first, daemon->namebuff);
      else
	{
	  /* no available server. */
	  ede = EDE_NOT_READY;
	  flags = 0;
	}
       
      /* don't forward A or AAAA queries for simple names, except the empty name */
      if (!flags &&
	  option_bool(OPT_NODOTS_LOCAL) &&
	  (gotname & (F_IPV4 | F_IPV6)) &&
	  !strchr(daemon->namebuff, '.') &&
	  strlen(daemon->namebuff) != 0)
	flags = check_for_local_domain(daemon->namebuff, now) ? F_NOERR : F_NXDOMAIN;
      
      /* Configured answer. */
      if (flags || ede == EDE_NOT_READY)
	goto reply;
      
      master = daemon->serverarray[first];
      
      if (!(forward = get_new_frec(now, master, 0)))
	goto reply;
      /* table full - flags == 0, return REFUSED */
      
      forward->frec_src.source = *udpaddr;
      forward->frec_src.orig_id = ntohs(header->id);
      forward->frec_src.dest = *dst_addr;
      forward->frec_src.iface = dst_iface;
      forward->frec_src.next = NULL;
      forward->frec_src.fd = udpfd;
      forward->new_id = get_id();
      memcpy(forward->hash, hash, HASH_SIZE);
      forward->forwardall = 0;
      forward->flags = fwd_flags;
      if (domain_no_rebind(daemon->namebuff))
	forward->flags |= FREC_NOREBIND;
      if (header->hb4 & HB4_CD)
	forward->flags |= FREC_CHECKING_DISABLED;
      if (ad_reqd)
	forward->flags |= FREC_AD_QUESTION;
#ifdef HAVE_DNSSEC
      forward->work_counter = DNSSEC_WORK;
      if (do_bit)
	forward->flags |= FREC_DO_QUESTION;
#endif
      
      start = first;

      if (option_bool(OPT_ALL_SERVERS))
	forward->forwardall = 1;

      if (!option_bool(OPT_ORDER))
	{
	  if (master->forwardcount++ > FORWARD_TEST ||
	      difftime(now, master->forwardtime) > FORWARD_TIME ||
	      master->last_server == -1)
	    {
	      master->forwardtime = now;
	      master->forwardcount = 0;
	      forward->forwardall = 1;
	    }
	  else
	    start = master->last_server;
	}
    }
  else
    {
      /* retry on existing query, from original source. Send to all available servers  */
#ifdef HAVE_DNSSEC
      /* If we've already got an answer to this query, but we're awaiting keys for validation,
	 there's no point retrying the query, retry the key query instead...... */
      if (forward->blocking_query)
	{
	  int is_sign;
	  unsigned char *pheader;
	  
	  while (forward->blocking_query)
	    forward = forward->blocking_query;
	   
	  blockdata_retrieve(forward->stash, forward->stash_len, (void *)header);
	  plen = forward->stash_len;
	  /* get query for logging. */
	  extract_request(header, plen, daemon->namebuff, NULL);
	  
	  if (find_pseudoheader(header, plen, NULL, &pheader, &is_sign, NULL) && !is_sign)
	    PUTSHORT(SAFE_PKTSZ, pheader);
	  
	  /* Find suitable servers: should never fail. */
	  if (!filter_servers(forward->sentto->arrayposn, F_DNSSECOK, &first, &last))
	    return 0;
	  
	  is_dnssec = 1;
	  forward->forwardall = 1;
	}
      else
#endif
	{
	  /* retry on existing query, from original source. Send to all available servers  */
	  forward->sentto->failed_queries++;
	  
	  if (!filter_servers(forward->sentto->arrayposn, F_SERVER, &first, &last))
	    goto reply;
	  
	  master = daemon->serverarray[first];
	  
	  /* Forward to all available servers on retry of query from same host. */
	  if (!option_bool(OPT_ORDER) && old_src)
	    forward->forwardall = 1;
	  else
	    {
	      start = forward->sentto->arrayposn;
	      
	      if (option_bool(OPT_ORDER))
		{
		  /* In strict order mode, there must be a server later in the list
		     left to send to, otherwise without the forwardall mechanism,
		     code further on will cycle around the list forwever if they
		     all return REFUSED. If at the last, give up. */
		  if (++start == last)
		    goto reply;
		}
	    }	  
	}
      
      /* If we didn't get an answer advertising a maximal packet in EDNS,
	 fall back to 1280, which should work everywhere on IPv6.
	 If that generates an answer, it will become the new default
	 for this server */
      forward->flags |= FREC_TEST_PKTSZ;
    }

  /* If a query is retried, use the log_id for the retry when logging the answer. */
  forward->frec_src.log_id = daemon->log_id;

  /* We may be resending a DNSSEC query here, for which the below processing is not necessary. */
  if (!is_dnssec)
    {
      header->id = htons(forward->new_id);
      
      plen = add_edns0_config(header, plen, ((unsigned char *)header) + PACKETSZ, &forward->frec_src.source, now, &subnet, &cacheable);
      
      if (subnet)
	forward->flags |= FREC_HAS_SUBNET;
      
      if (!cacheable)
	forward->flags |= FREC_NO_CACHE;
      
#ifdef HAVE_DNSSEC
      if (option_bool(OPT_DNSSEC_VALID) && (master->flags & SERV_DO_DNSSEC))
	{
	  plen = add_do_bit(header, plen, ((unsigned char *) header) + PACKETSZ);
	  
	  /* For debugging, set Checking Disabled, otherwise, have the upstream check too,
	     this allows it to select auth servers when one is returning bad data. */
	  if (option_bool(OPT_DNSSEC_DEBUG))
	    header->hb4 |= HB4_CD;
	  
	}
#endif
      
      if (find_pseudoheader(header, plen, &edns0_len, &pheader, NULL, NULL))
	{
	  /* If there wasn't a PH before, and there is now, we added it. */
	  if (!oph)
	    forward->flags |= FREC_ADDED_PHEADER;
	  
	  /* If we're sending an EDNS0 with any options, we can't recreate the query from a reply. */
	  if (edns0_len > 11)
	    forward->flags |= FREC_HAS_EXTRADATA;
	  
	  /* Reduce udp size on retransmits. */
	  if (forward->flags & FREC_TEST_PKTSZ)
	    PUTSHORT(SAFE_PKTSZ, pheader);
	}
    }
  
  if (forward->forwardall)
    start = first;

  forwarded = 0;
  
  /* check for send errors here (no route to host) 
     if we fail to send to all nameservers, send back an error
     packet straight away (helps modem users when offline)  */

  while (1)
    { 
      int fd;
      struct server *srv = daemon->serverarray[start];
      
      if ((fd = allocate_rfd(&forward->rfds, srv)) != -1)
	{
	  
#ifdef HAVE_CONNTRACK
	  /* Copy connection mark of incoming query to outgoing connection. */
	  if (option_bool(OPT_CONNTRACK))
	    set_outgoing_mark(forward, fd);
#endif
	  
#ifdef HAVE_DNSSEC
	  if (option_bool(OPT_DNSSEC_VALID) && (forward->flags & FREC_ADDED_PHEADER))
	    {
	      /* Difficult one here. If our client didn't send EDNS0, we will have set the UDP
		 packet size to 512. But that won't provide space for the RRSIGS in many cases.
		 The RRSIGS will be stripped out before the answer goes back, so the packet should
		 shrink again. So, if we added a do-bit, bump the udp packet size to the value
		 known to be OK for this server. We check returned size after stripping and set
		 the truncated bit if it's still too big. */		  
	      unsigned char *pheader;
	      int is_sign;
	      if (find_pseudoheader(header, plen, NULL, &pheader, &is_sign, NULL) && !is_sign)
		PUTSHORT(srv->edns_pktsz, pheader);
	    }
#endif
	  
	  if (retry_send(sendto(fd, (char *)header, plen, 0,
				&srv->addr.sa,
				sa_len(&srv->addr))))
	    continue;
	  
	  if (errno == 0)
	    {
#ifdef HAVE_DUMPFILE
	      dump_packet(DUMP_UP_QUERY, (void *)header, plen, NULL, &srv->addr);
#endif
	      
	      /* Keep info in case we want to re-send this packet */
	      daemon->srv_save = srv;
	      daemon->packet_len = plen;
	      daemon->fd_save = fd;
	      
	      if (!(forward->flags & (FREC_DNSKEY_QUERY | FREC_DS_QUERY)))
		{
		  if (!gotname)
		    strcpy(daemon->namebuff, "query");
		  log_query_mysockaddr(F_SERVER | F_FORWARD, daemon->namebuff,
				       &srv->addr, NULL);
		}
#ifdef HAVE_DNSSEC
	      else
		log_query_mysockaddr(F_NOEXTRA | F_DNSSEC, daemon->namebuff, &srv->addr,
				     querystr("dnssec-retry", (forward->flags & FREC_DNSKEY_QUERY) ? T_DNSKEY : T_DS));
#endif

	      srv->queries++;
	      forwarded = 1;
	      forward->sentto = srv;
	      if (!forward->forwardall) 
		break;
	      forward->forwardall++;
	    }
	}
      
      if (++start == last)
	break;
    }
  
  if (forwarded || is_dnssec)
    return 1;
  
  /* could not send on, prepare to return */ 
  header->id = htons(forward->frec_src.orig_id);
  free_frec(forward); /* cancel */
  ede = EDE_NETERR;
  
 reply:
  if (udpfd != -1)
    {
      if (!(plen = make_local_answer(flags, gotname, plen, header, daemon->namebuff, limit, first, last, ede)))
	return 0;
      
      if (oph)
	{
	  u16 swap = htons((u16)ede);

	  if (ede != EDE_UNSET)
	    plen = add_pseudoheader(header, plen, (unsigned char *)limit, daemon->edns_pktsz, EDNS0_OPTION_EDE, (unsigned char *)&swap, 2, do_bit, 0);
	  else
	    plen = add_pseudoheader(header, plen, (unsigned char *)limit, daemon->edns_pktsz, 0, NULL, 0, do_bit, 0);
	}
      
#if defined(HAVE_CONNTRACK) && defined(HAVE_UBUS)
      if (option_bool(OPT_CMARK_ALST_EN))
	{
	  unsigned int mark;
	  int have_mark = get_incoming_mark(udpaddr, dst_addr, /* istcp: */ 0, &mark);
	  if (have_mark && ((u32)mark & daemon->allowlist_mask))
	    report_addresses(header, plen, mark);
	}
#endif
      
      send_from(udpfd, option_bool(OPT_NOWILD) || option_bool(OPT_CLEVERBIND), (char *)header, plen, udpaddr, dst_addr, dst_iface);
    }
	  
  return 0;
}

static size_t process_reply(struct dns_header *header, time_t now, struct server *server, size_t n, int check_rebind, 
			    int no_cache, int cache_secure, int bogusanswer, int ad_reqd, int do_bit, int added_pheader, 
			    int check_subnet, union mysockaddr *query_source, unsigned char *limit, int ede)
{
  unsigned char *pheader, *sizep;
  char **sets = 0;
  int munged = 0, is_sign;
  unsigned int rcode = RCODE(header);
  size_t plen; 
    
  (void)ad_reqd;
  (void)do_bit;
  (void)bogusanswer;

#ifdef HAVE_IPSET
  if (daemon->ipsets && extract_request(header, n, daemon->namebuff, NULL))
    {
      /* Similar algorithm to search_servers. */
      struct ipsets *ipset_pos;
      unsigned int namelen = strlen(daemon->namebuff);
      unsigned int matchlen = 0;
      for (ipset_pos = daemon->ipsets; ipset_pos; ipset_pos = ipset_pos->next) 
	{
	  unsigned int domainlen = strlen(ipset_pos->domain);
	  char *matchstart = daemon->namebuff + namelen - domainlen;
	  if (namelen >= domainlen && hostname_isequal(matchstart, ipset_pos->domain) &&
	      (domainlen == 0 || namelen == domainlen || *(matchstart - 1) == '.' ) &&
	      domainlen >= matchlen) 
	    {
	      matchlen = domainlen;
	      sets = ipset_pos->sets;
	    }
	}
    }
#endif

  if ((pheader = find_pseudoheader(header, n, &plen, &sizep, &is_sign, NULL)))
    {
      /* Get extended RCODE. */
      rcode |= sizep[2] << 4;

      if (check_subnet && !check_source(header, plen, pheader, query_source))
	{
	  my_syslog(LOG_WARNING, _("discarding DNS reply: subnet option mismatch"));
	  return 0;
	}
      
      if (!is_sign)
	{
	  if (added_pheader)
	    {
	      /* client didn't send EDNS0, we added one, strip it off before returning answer. */
	      n = rrfilter(header, n, 0);
	      pheader = NULL;
	    }
	  else
	    {
	      /* If upstream is advertising a larger UDP packet size
		 than we allow, trim it so that we don't get overlarge
		 requests for the client. We can't do this for signed packets. */
	      unsigned short udpsz;
	      GETSHORT(udpsz, sizep);
	      if (udpsz > daemon->edns_pktsz)
		{
		  sizep -= 2;
		  PUTSHORT(daemon->edns_pktsz, sizep);
		}

#ifdef HAVE_DNSSEC
	      /* If the client didn't set the do bit, but we did, reset it. */
	      if (option_bool(OPT_DNSSEC_VALID) && !do_bit)
		{
		  unsigned short flags;
		  sizep += 2; /* skip RCODE */
		  GETSHORT(flags, sizep);
		  flags &= ~0x8000;
		  sizep -= 2;
		  PUTSHORT(flags, sizep);
		}
#endif
	    }
	}
    }
  
  /* RFC 4035 sect 4.6 para 3 */
  if (!is_sign && !option_bool(OPT_DNSSEC_PROXY))
     header->hb4 &= ~HB4_AD;

  header->hb4 |= HB4_RA; /* recursion if available */

  if (OPCODE(header) != QUERY)
    return resize_packet(header, n, pheader, plen);

  if (rcode != NOERROR && rcode != NXDOMAIN)
    {
      union all_addr a;
      a.log.rcode = rcode;
      a.log.ede = ede;
      log_query(F_UPSTREAM | F_RCODE, "error", &a, NULL);
      
      return resize_packet(header, n, pheader, plen);
    }
  
  /* Complain loudly if the upstream server is non-recursive. */
  if (!(header->hb4 & HB4_RA) && rcode == NOERROR &&
      server && !(server->flags & SERV_WARNED_RECURSIVE))
    {
      (void)prettyprint_addr(&server->addr, daemon->namebuff);
      my_syslog(LOG_WARNING, _("nameserver %s refused to do a recursive query"), daemon->namebuff);
      if (!option_bool(OPT_LOG))
	server->flags |= SERV_WARNED_RECURSIVE;
    }  

  if (daemon->bogus_addr && rcode != NXDOMAIN &&
      check_for_bogus_wildcard(header, n, daemon->namebuff, now))
    {
      munged = 1;
      SET_RCODE(header, NXDOMAIN);
      header->hb3 &= ~HB3_AA;
      cache_secure = 0;
      ede = EDE_BLOCKED;
    }
  else 
    {
      int doctored = 0;
      
      if (rcode == NXDOMAIN && 
	  extract_request(header, n, daemon->namebuff, NULL))
	{
	  if (check_for_local_domain(daemon->namebuff, now) ||
	      lookup_domain(daemon->namebuff, F_CONFIG, NULL, NULL))
	    {
	      /* if we forwarded a query for a locally known name (because it was for 
		 an unknown type) and the answer is NXDOMAIN, convert that to NODATA,
		 since we know that the domain exists, even if upstream doesn't */
	      munged = 1;
	      header->hb3 |= HB3_AA;
	      SET_RCODE(header, NOERROR);
	      cache_secure = 0;
	    }
	}
      
      if (extract_addresses(header, n, daemon->namebuff, now, sets, is_sign, check_rebind, no_cache, cache_secure, &doctored))
	{
	  my_syslog(LOG_WARNING, _("possible DNS-rebind attack detected: %s"), daemon->namebuff);
	  munged = 1;
	  cache_secure = 0;
	  ede = EDE_BLOCKED;
	}

      if (doctored)
	cache_secure = 0;
    }
  
#ifdef HAVE_DNSSEC
  if (bogusanswer && !(header->hb4 & HB4_CD) && !option_bool(OPT_DNSSEC_DEBUG))
    {
      /* Bogus reply, turn into SERVFAIL */
      SET_RCODE(header, SERVFAIL);
      munged = 1;
    }

  if (option_bool(OPT_DNSSEC_VALID))
    {
      header->hb4 &= ~HB4_AD;
      
      if (!(header->hb4 & HB4_CD) && ad_reqd && cache_secure)
	header->hb4 |= HB4_AD;
      
      /* If the requestor didn't set the DO bit, don't return DNSSEC info. */
      if (!do_bit)
	n = rrfilter(header, n, 1);
    }
#endif

  /* do this after extract_addresses. Ensure NODATA reply and remove
     nameserver info. */
  if (munged)
    {
      header->ancount = htons(0);
      header->nscount = htons(0);
      header->arcount = htons(0);
      header->hb3 &= ~HB3_TC;
    }
  
  /* the bogus-nxdomain stuff, doctor and NXDOMAIN->NODATA munging can all elide
     sections of the packet. Find the new length here and put back pseudoheader
     if it was removed. */
  n = resize_packet(header, n, pheader, plen);

  if (pheader && ede != EDE_UNSET)
    {
      u16 swap = htons((u16)ede);
      n = add_pseudoheader(header, n, limit, daemon->edns_pktsz, EDNS0_OPTION_EDE, (unsigned char *)&swap, 2, do_bit, 1);
    }
  
  return n;
}

#ifdef HAVE_DNSSEC
static void dnssec_validate(struct frec *forward, struct dns_header *header,
			    ssize_t plen, int status, time_t now)
{
  daemon->log_display_id = forward->frec_src.log_id;
  
  /* We've had a reply already, which we're validating. Ignore this duplicate */
  if (forward->blocking_query)
    return;
  
  /* Truncated answer can't be validated.
     If this is an answer to a DNSSEC-generated query, we still
     need to get the client to retry over TCP, so return
     an answer with the TC bit set, even if the actual answer fits.
  */
  if (header->hb3 & HB3_TC)
    status = STAT_TRUNCATED;

  /* If all replies to a query are REFUSED, give up. */
  if (RCODE(header) == REFUSED)
    status = STAT_ABANDONED;
  
  /* As soon as anything returns BOGUS, we stop and unwind, to do otherwise
     would invite infinite loops, since the answers to DNSKEY and DS queries
     will not be cached, so they'll be repeated. */
  if (!STAT_ISEQUAL(status, STAT_BOGUS) && !STAT_ISEQUAL(status, STAT_TRUNCATED) && !STAT_ISEQUAL(status, STAT_ABANDONED))
    {
      if (forward->flags & FREC_DNSKEY_QUERY)
	status = dnssec_validate_by_ds(now, header, plen, daemon->namebuff, daemon->keyname, forward->class);
      else if (forward->flags & FREC_DS_QUERY)
	status = dnssec_validate_ds(now, header, plen, daemon->namebuff, daemon->keyname, forward->class);
      else
	status = dnssec_validate_reply(now, header, plen, daemon->namebuff, daemon->keyname, &forward->class, 
				       !option_bool(OPT_DNSSEC_IGN_NS) && (forward->sentto->flags & SERV_DO_DNSSEC),
				       NULL, NULL, NULL);
#ifdef HAVE_DUMPFILE
      if (STAT_ISEQUAL(status, STAT_BOGUS))
	dump_packet((forward->flags & (FREC_DNSKEY_QUERY | FREC_DS_QUERY)) ? DUMP_SEC_BOGUS : DUMP_BOGUS,
		    header, (size_t)plen, &forward->sentto->addr, NULL);
#endif
    }
  
  /* Can't validate, as we're missing key data. Put this
     answer aside, whilst we get that. */     
  if (STAT_ISEQUAL(status, STAT_NEED_DS) || STAT_ISEQUAL(status, STAT_NEED_KEY))
    {
      struct frec *new = NULL;
      int serverind;
      struct blockdata *stash;
      
      /* Now save reply pending receipt of key data */
      if ((serverind = dnssec_server(forward->sentto, daemon->keyname, NULL, NULL)) != -1 &&
	  (stash = blockdata_alloc((char *)header, plen)))
	{
	  struct server *server = daemon->serverarray[serverind];
	  struct frec *orig;
	  unsigned int flags;
	  void *hash;
	  size_t nn;

	  /* validate routines leave name of required record in daemon->keyname */
	  nn = dnssec_generate_query(header, ((unsigned char *) header) + server->edns_pktsz,
				     daemon->keyname, forward->class,
				     STAT_ISEQUAL(status, STAT_NEED_KEY) ? T_DNSKEY : T_DS, server->edns_pktsz);
	  
	  flags = STAT_ISEQUAL(status, STAT_NEED_KEY) ? FREC_DNSKEY_QUERY : FREC_DS_QUERY;
	  hash = hash_questions(header, nn, daemon->namebuff);

	  if ((new = lookup_frec_by_query(hash, flags, FREC_DNSKEY_QUERY | FREC_DS_QUERY)))
	    {
	      forward->next_dependent = new->dependent;
	      new->dependent = forward;
	      /* Make consistent, only replace query copy with unvalidated answer
		 when we set ->blocking_query. */
	      if (forward->stash)
		blockdata_free(forward->stash);
	      forward->blocking_query = new;
	      forward->stash_len = plen;
	      forward->stash = stash;
	      return;
	    }
	    
	  /* Find the original query that started it all.... */
	  for (orig = forward; orig->dependent; orig = orig->dependent);
	  
	  /* Make sure we don't expire and free the orig frec during the
	     allocation of a new one: third arg of get_new_frec() does that. */
	  if (--orig->work_counter == 0 || !(new = get_new_frec(now, server, 1)))
	    blockdata_free(stash); /* don't leak this on failure. */
	  else
	    {
	      int fd;
	      struct frec *next = new->next;

	      *new = *forward; /* copy everything, then overwrite */
	      new->next = next;
	      new->blocking_query = NULL;
	      
	      new->frec_src.log_id = daemon->log_display_id = ++daemon->log_id;
	      new->sentto = server;
	      new->rfds = NULL;
	      new->frec_src.next = NULL;
	      new->flags &= ~(FREC_DNSKEY_QUERY | FREC_DS_QUERY | FREC_HAS_EXTRADATA);
	      new->flags |= flags;
	      new->forwardall = 0;
	      
	      forward->next_dependent = NULL;
	      new->dependent = forward; /* to find query awaiting new one. */

	      /* Make consistent, only replace query copy with unvalidated answer
		 when we set ->blocking_query. */
	      forward->blocking_query = new; 
	      if (forward->stash)
		blockdata_free(forward->stash);
	      forward->stash_len = plen;
	      forward->stash = stash;
	      
	      memcpy(new->hash, hash, HASH_SIZE);
	      new->new_id = get_id();
	      header->id = htons(new->new_id);
	      /* Save query for retransmission */
	      new->stash = blockdata_alloc((char *)header, nn);
	      new->stash_len = nn;
	      
	      /* Don't resend this. */
	      daemon->srv_save = NULL;
	      
	      if ((fd = allocate_rfd(&new->rfds, server)) != -1)
		{
#ifdef HAVE_CONNTRACK
		  if (option_bool(OPT_CONNTRACK))
		    set_outgoing_mark(orig, fd);
#endif
		  server_send_log(server, fd, header, nn, DUMP_SEC_QUERY,
				  F_NOEXTRA | F_DNSSEC, daemon->keyname,
				  querystr("dnssec-query", STAT_ISEQUAL(status, STAT_NEED_KEY) ? T_DNSKEY : T_DS));
		  server->queries++;
		}
	      
	      return;
	    }
	}

      /* sending DNSSEC query failed. */
      status = STAT_ABANDONED;
    }
  
  /* Validated original answer, all done. */
  if (!forward->dependent)
    return_reply(now, forward, header, plen, status);
  else
    {
      /* validated subsidiary query/queries, (and cached result)
	 pop that and return to the previous query/queries we were working on. */
      struct frec *prev, *nxt = forward->dependent;
      
      free_frec(forward);
      
      while ((prev = nxt))
	{
	  /* ->next_dependent will have changed after return from recursive call below. */
	  nxt = prev->next_dependent;
	  prev->blocking_query = NULL; /* already gone */
	  blockdata_retrieve(prev->stash, prev->stash_len, (void *)header);
	  dnssec_validate(prev, header, prev->stash_len, status, now);
	}
    }
}
#endif

/* sets new last_server */
void reply_query(int fd, time_t now)
{
  /* packet from peer server, extract data for cache, and send to
     original requester */
  struct dns_header *header;
  union mysockaddr serveraddr;
  struct frec *forward;
  socklen_t addrlen = sizeof(serveraddr);
  ssize_t n = recvfrom(fd, daemon->packet, daemon->packet_buff_sz, 0, &serveraddr.sa, &addrlen);
  struct server *server;
  void *hash;
  int first, last, c;
    
  /* packet buffer overwritten */
  daemon->srv_save = NULL;

  /* Determine the address of the server replying  so that we can mark that as good */
  if (serveraddr.sa.sa_family == AF_INET6)
    serveraddr.in6.sin6_flowinfo = 0;
  
  header = (struct dns_header *)daemon->packet;

  if (n < (int)sizeof(struct dns_header) || !(header->hb3 & HB3_QR))
    return;

  hash = hash_questions(header, n, daemon->namebuff);
  
  if (!(forward = lookup_frec(ntohs(header->id), fd, hash, &first, &last)))
    return;
  
  /* spoof check: answer must come from known server, also
     we may have sent the same query to multiple servers from
     the same local socket, and would like to know which one has answered. */
  for (c = first; c != last; c++)
    if (sockaddr_isequal(&daemon->serverarray[c]->addr, &serveraddr))
      break;
  
  if (c == last)
    return;

  server = daemon->serverarray[c];

  if (RCODE(header) != REFUSED)
    daemon->serverarray[first]->last_server = c;
  else if (daemon->serverarray[first]->last_server == c)
    daemon->serverarray[first]->last_server = -1;

  /* If sufficient time has elapsed, try and expand UDP buffer size again. */
  if (difftime(now, server->pktsz_reduced) > UDP_TEST_TIME)
    server->edns_pktsz = daemon->edns_pktsz;

#ifdef HAVE_DUMPFILE
  dump_packet((forward->flags & (FREC_DNSKEY_QUERY | FREC_DS_QUERY)) ? DUMP_SEC_REPLY : DUMP_UP_REPLY,
	      (void *)header, n, &serveraddr, NULL);
#endif

  /* log_query gets called indirectly all over the place, so 
     pass these in global variables - sorry. */
  daemon->log_display_id = forward->frec_src.log_id;
  daemon->log_source_addr = &forward->frec_src.source;
  
  if (daemon->ignore_addr && RCODE(header) == NOERROR &&
      check_for_ignored_address(header, n))
    return;

  /* Note: if we send extra options in the EDNS0 header, we can't recreate
     the query from the reply. */
  if ((RCODE(header) == REFUSED || RCODE(header) == SERVFAIL) &&
      forward->forwardall == 0 &&
      !(forward->flags & FREC_HAS_EXTRADATA))
    /* for broken servers, attempt to send to another one. */
    {
      unsigned char *pheader, *udpsz;
      unsigned short udp_size =  PACKETSZ; /* default if no EDNS0 */
      size_t plen;
      int is_sign;
      size_t nn = 0;
      
#ifdef HAVE_DNSSEC
      /* DNSSEC queries have a copy of the original query stashed. 
	 The query MAY have got a good answer, and be awaiting
	 the results of further queries, in which case
	 The Stash contains something else and we don't need to retry anyway. */
      if ((forward->flags & (FREC_DNSKEY_QUERY | FREC_DS_QUERY)) && !forward->blocking_query)
	{
	  blockdata_retrieve(forward->stash, forward->stash_len, (void *)header);
	  nn = forward->stash_len;
	  udp_size = daemon->edns_pktsz;
	}
      else
#endif
	{
	  /* recreate query from reply */
	  if ((pheader = find_pseudoheader(header, (size_t)n, &plen, &udpsz, &is_sign, NULL)))
	    GETSHORT(udp_size, udpsz);
	  
	  /* If the client provides an EDNS0 UDP size, use that to limit our reply.
	     (bounded by the maximum configured). If no EDNS0, then it
	     defaults to 512 */
	  if (udp_size > daemon->edns_pktsz)
	    udp_size = daemon->edns_pktsz;
	  else if (udp_size < PACKETSZ)
	    udp_size = PACKETSZ; /* Sanity check - can't reduce below default. RFC 6891 6.2.3 */
	  
	  if (!is_sign &&
	      (nn = resize_packet(header, (size_t)n, pheader, plen)) &&
	      (forward->flags & FREC_DO_QUESTION))
	    add_do_bit(header, nn,  (unsigned char *)pheader + plen);

	  header->ancount = htons(0);
	  header->nscount = htons(0);
	  header->arcount = htons(0);
	  header->hb3 &= ~(HB3_QR | HB3_AA | HB3_TC);
	  header->hb4 &= ~(HB4_RA | HB4_RCODE | HB4_CD | HB4_AD);
	  if (forward->flags & FREC_CHECKING_DISABLED)
	    header->hb4 |= HB4_CD;
	  if (forward->flags & FREC_AD_QUESTION)
	    header->hb4 |= HB4_AD;
	}

      if (nn)
	{
	  forward_query(-1, NULL, NULL, 0, header, nn, ((char *) header) + udp_size, now, forward,
			forward->flags & FREC_AD_QUESTION, forward->flags & FREC_DO_QUESTION);
	  return;
	}
    }

  /* If the answer is an error, keep the forward record in place in case
     we get a good reply from another server. Kill it when we've
     had replies from all to avoid filling the forwarding table when
     everything is broken */

  /* decrement count of replies recieved if we sent to more than one server. */
  if (forward->forwardall && (--forward->forwardall > 1) && RCODE(header) == REFUSED)
    return;

  /* We tried resending to this server with a smaller maximum size and got an answer.
     Make that permanent. To avoid reduxing the packet size for a single dropped packet,
     only do this when we get a truncated answer, or one larger than the safe size. */
  if (server->edns_pktsz > SAFE_PKTSZ && (forward->flags & FREC_TEST_PKTSZ) && 
      ((header->hb3 & HB3_TC) || n >= SAFE_PKTSZ))
    {
      server->edns_pktsz = SAFE_PKTSZ;
      server->pktsz_reduced = now;
      (void)prettyprint_addr(&server->addr, daemon->addrbuff);
      my_syslog(LOG_WARNING, _("reducing DNS packet size for nameserver %s to %d"), daemon->addrbuff, SAFE_PKTSZ);
    }

  forward->sentto = server;
  
#ifdef HAVE_DNSSEC
  if ((forward->sentto->flags & SERV_DO_DNSSEC) && 
      option_bool(OPT_DNSSEC_VALID) &&
      !(forward->flags & FREC_CHECKING_DISABLED))
    dnssec_validate(forward, header, n, STAT_OK, now);
  else
#endif
    return_reply(now, forward, header, n, STAT_OK); 
}

static void return_reply(time_t now, struct frec *forward, struct dns_header *header, ssize_t n, int status)
{
  int check_rebind = 0, no_cache_dnssec = 0, cache_secure = 0, bogusanswer = 0;
  size_t nn;
  int ede = EDE_UNSET;

  (void)status;

  daemon->log_display_id = forward->frec_src.log_id;
  daemon->log_source_addr = &forward->frec_src.source;
  
  /* Don't cache replies where DNSSEC validation was turned off, either
     the upstream server told us so, or the original query specified it.  */
  if ((header->hb4 & HB4_CD) || (forward->flags & FREC_CHECKING_DISABLED))
    no_cache_dnssec = 1;

#ifdef HAVE_DNSSEC
  if (!STAT_ISEQUAL(status, STAT_OK))
    {
      /* status is STAT_OK when validation not turned on. */
      no_cache_dnssec = 0;
      
      if (STAT_ISEQUAL(status, STAT_TRUNCATED))
	header->hb3 |= HB3_TC;
      else
	{
	  char *result, *domain = "result";
	  union all_addr a;

	  a.log.ede = ede = errflags_to_ede(status);

	  if (STAT_ISEQUAL(status, STAT_ABANDONED))
	    {
	      result = "ABANDONED";
	      status = STAT_BOGUS;
	    }
	  else
	    result = (STAT_ISEQUAL(status, STAT_SECURE) ? "SECURE" : (STAT_ISEQUAL(status, STAT_INSECURE) ? "INSECURE" : "BOGUS"));
	  
	  if (STAT_ISEQUAL(status, STAT_SECURE))
	    cache_secure = 1;
	  else if (STAT_ISEQUAL(status, STAT_BOGUS))
	    {
	      no_cache_dnssec = 1;
	      bogusanswer = 1;
	      
	      if (extract_request(header, n, daemon->namebuff, NULL))
		domain = daemon->namebuff;
	    }
	  
	  log_query(F_SECSTAT, domain, &a, result);
	}
    }
#endif
  
  if (option_bool(OPT_NO_REBIND))
    check_rebind = !(forward->flags & FREC_NOREBIND);
  
  /* restore CD bit to the value in the query */
  if (forward->flags & FREC_CHECKING_DISABLED)
    header->hb4 |= HB4_CD;
  else
    header->hb4 &= ~HB4_CD;
  
  /* Never cache answers which are contingent on the source or MAC address EDSN0 option,
     since the cache is ignorant of such things. */
  if (forward->flags & FREC_NO_CACHE)
    no_cache_dnssec = 1;
  
  if ((nn = process_reply(header, now, forward->sentto, (size_t)n, check_rebind, no_cache_dnssec, cache_secure, bogusanswer, 
			  forward->flags & FREC_AD_QUESTION, forward->flags & FREC_DO_QUESTION, 
			  forward->flags & FREC_ADDED_PHEADER, forward->flags & FREC_HAS_SUBNET, &forward->frec_src.source,
			  ((unsigned char *)header) + daemon->edns_pktsz, ede)))
    {
      struct frec_src *src;
      
      header->id = htons(forward->frec_src.orig_id);
#ifdef HAVE_DNSSEC
      /* We added an EDNSO header for the purpose of getting DNSSEC RRs, and set the value of the UDP payload size
	 greater than the no-EDNS0-implied 512 to have space for the RRSIGS. If, having stripped them and the EDNS0
	 header, the answer is still bigger than 512, truncate it and mark it so. The client then retries with TCP. */
      if (option_bool(OPT_DNSSEC_VALID) && (forward->flags & FREC_ADDED_PHEADER) && (nn > PACKETSZ))
	{
	  header->ancount = htons(0);
	  header->nscount = htons(0);
	  header->arcount = htons(0);
	  header->hb3 |= HB3_TC;
	  nn = resize_packet(header, nn, NULL, 0);
	}
#endif
      
      for (src = &forward->frec_src; src; src = src->next)
	{
	  header->id = htons(src->orig_id);
	  
#ifdef HAVE_DUMPFILE
	  dump_packet(DUMP_REPLY, daemon->packet, (size_t)nn, NULL, &src->source);
#endif
	  
#if defined(HAVE_CONNTRACK) && defined(HAVE_UBUS)
	  if (option_bool(OPT_CMARK_ALST_EN))
	    {
	      unsigned int mark;
	      int have_mark = get_incoming_mark(&src->source, &src->dest, /* istcp: */ 0, &mark);
	      if (have_mark && ((u32)mark & daemon->allowlist_mask))
		report_addresses(header, nn, mark);
	    }
#endif
	  
	  send_from(src->fd, option_bool(OPT_NOWILD) || option_bool (OPT_CLEVERBIND), daemon->packet, nn, 
		    &src->source, &src->dest, src->iface);
	  
	  if (option_bool(OPT_EXTRALOG) && src != &forward->frec_src)
	    {
	      daemon->log_display_id = src->log_id;
	      daemon->log_source_addr = &src->source;
	      log_query(F_UPSTREAM, "query", NULL, "duplicate");
	    }
	}
    }

  free_frec(forward); /* cancel */
}


#ifdef HAVE_CONNTRACK
static int is_query_allowed_for_mark(u32 mark, const char *name)
{
  int is_allowable_name, did_validate_name = 0;
  struct allowlist *allowlists;
  char **patterns_pos;
  
  for (allowlists = daemon->allowlists; allowlists; allowlists = allowlists->next)
    if (allowlists->mark == (mark & daemon->allowlist_mask & allowlists->mask))
      for (patterns_pos = allowlists->patterns; *patterns_pos; patterns_pos++)
	{
	  if (!strcmp(*patterns_pos, "*"))
	    return 1;
	  if (!did_validate_name)
	    {
	      is_allowable_name = name ? is_valid_dns_name(name) : 0;
	      did_validate_name = 1;
	    }
	  if (is_allowable_name && is_dns_name_matching_pattern(name, *patterns_pos))
	    return 1;
	}
  return 0;
}

static size_t answer_disallowed(struct dns_header *header, size_t qlen, u32 mark, const char *name)
{
  unsigned char *p;
  (void)name;
  (void)mark;
  
#ifdef HAVE_UBUS
  if (name)
    ubus_event_bcast_connmark_allowlist_refused(mark, name);
#endif
  
  setup_reply(header, /* flags: */ 0, EDE_BLOCKED);
  
  if (!(p = skip_questions(header, qlen)))
    return 0;
  return p - (unsigned char *)header;
}
#endif

void receive_query(struct listener *listen, time_t now)
{
  struct dns_header *header = (struct dns_header *)daemon->packet;
  union mysockaddr source_addr;
  unsigned char *pheader;
  unsigned short type, udp_size = PACKETSZ; /* default if no EDNS0 */
  union all_addr dst_addr;
  struct in_addr netmask, dst_addr_4;
  size_t m,plen;
  ssize_t n,num;
  int if_index = 0, auth_dns = 0, do_bit = 0, have_pseudoheader = 0;
#ifdef HAVE_CONNTRACK
  unsigned int mark = 0;
  int have_mark = 0;
  int is_single_query = 0, allowed = 1;
#endif
#ifdef HAVE_AUTH
  int local_auth = 0;
#endif
  struct iovec iov[1];
  struct msghdr msg;
  struct cmsghdr *cmptr;
  union {
    struct cmsghdr align; /* this ensures alignment */
    char control6[CMSG_SPACE(sizeof(struct in6_pktinfo))];
#if defined(HAVE_LINUX_NETWORK)
    char control[CMSG_SPACE(sizeof(struct in_pktinfo))];
#elif defined(IP_RECVDSTADDR) && defined(HAVE_SOLARIS_NETWORK)
    char control[CMSG_SPACE(sizeof(struct in_addr)) +
		 CMSG_SPACE(sizeof(unsigned int))];
#elif defined(IP_RECVDSTADDR)
    char control[CMSG_SPACE(sizeof(struct in_addr)) +
		 CMSG_SPACE(sizeof(struct sockaddr_dl))];
#endif
  } control_u;
  int family = listen->addr.sa.sa_family;
   /* Can always get recvd interface for IPv6 */
  int check_dst = !option_bool(OPT_NOWILD) || family == AF_INET6;

  /* packet buffer overwritten */
  daemon->srv_save = NULL;

  dst_addr_4.s_addr = dst_addr.addr4.s_addr = 0;
  netmask.s_addr = 0;
  
  if (option_bool(OPT_NOWILD) && listen->iface)
    {
      auth_dns = listen->iface->dns_auth;
     
      if (family == AF_INET)
	{
	  dst_addr_4 = dst_addr.addr4 = listen->iface->addr.in.sin_addr;
	  netmask = listen->iface->netmask;
	}
    }
  
  iov[0].iov_base = daemon->packet;
  iov[0].iov_len = daemon->edns_pktsz;
    
  msg.msg_control = control_u.control;
  msg.msg_controllen = sizeof(control_u);
  msg.msg_flags = 0;
  msg.msg_name = &source_addr;
  msg.msg_namelen = sizeof(source_addr);
  msg.msg_iov = iov;
  msg.msg_iovlen = 1;
  
  if ((n = recvmsg(listen->fd, &msg, 0)) == -1)
    return;
  
  if (n < (int)sizeof(struct dns_header) || 
      (msg.msg_flags & MSG_TRUNC) ||
      (header->hb3 & HB3_QR))
    return;
	num=n;
  /* Clear buffer beyond request to avoid risk of
     information disclosure. */
  memset(daemon->packet + n, 0, daemon->edns_pktsz - n);
  
  source_addr.sa.sa_family = family;
  
  if (family == AF_INET)
    {
       /* Source-port == 0 is an error, we can't send back to that. 
	  http://www.ietf.org/mail-archive/web/dnsop/current/msg11441.html */
      if (source_addr.in.sin_port == 0)
	return;
    }
  else
    {
      /* Source-port == 0 is an error, we can't send back to that. */
      if (source_addr.in6.sin6_port == 0)
	return;
      source_addr.in6.sin6_flowinfo = 0;
    }
  
  /* We can be configured to only accept queries from at-most-one-hop-away addresses. */
  if (option_bool(OPT_LOCAL_SERVICE))
    {
      struct addrlist *addr;

      if (family == AF_INET6) 
	{
	  for (addr = daemon->interface_addrs; addr; addr = addr->next)
	    if ((addr->flags & ADDRLIST_IPV6) &&
		is_same_net6(&addr->addr.addr6, &source_addr.in6.sin6_addr, addr->prefixlen))
	      break;
	}
      else
	{
	  struct in_addr netmask;
	  for (addr = daemon->interface_addrs; addr; addr = addr->next)
	    {
	      netmask.s_addr = htonl(~(in_addr_t)0 << (32 - addr->prefixlen));
	      if (!(addr->flags & ADDRLIST_IPV6) &&
		  is_same_net(addr->addr.addr4, source_addr.in.sin_addr, netmask))
		break;
	    }
	}
      if (!addr)
	{
	  static int warned = 0;
	  if (!warned)
	    {
	      my_syslog(LOG_WARNING, _("Ignoring query from non-local network"));
	      warned = 1;
	    }
	  return;
	}
    }
		
  if (check_dst)
    {
      struct ifreq ifr;

      if (msg.msg_controllen < sizeof(struct cmsghdr))
	return;

#if defined(HAVE_LINUX_NETWORK)
      if (family == AF_INET)
	for (cmptr = CMSG_FIRSTHDR(&msg); cmptr; cmptr = CMSG_NXTHDR(&msg, cmptr))
	  if (cmptr->cmsg_level == IPPROTO_IP && cmptr->cmsg_type == IP_PKTINFO)
	    {
	      union {
		unsigned char *c;
		struct in_pktinfo *p;
	      } p;
	      p.c = CMSG_DATA(cmptr);
	      dst_addr_4 = dst_addr.addr4 = p.p->ipi_spec_dst;
	      if_index = p.p->ipi_ifindex;
	    }
#elif defined(IP_RECVDSTADDR) && defined(IP_RECVIF)
      if (family == AF_INET)
	{
	  for (cmptr = CMSG_FIRSTHDR(&msg); cmptr; cmptr = CMSG_NXTHDR(&msg, cmptr))
	    {
	      union {
		unsigned char *c;
		unsigned int *i;
		struct in_addr *a;
#ifndef HAVE_SOLARIS_NETWORK
		struct sockaddr_dl *s;
#endif
	      } p;
	       p.c = CMSG_DATA(cmptr);
	       if (cmptr->cmsg_level == IPPROTO_IP && cmptr->cmsg_type == IP_RECVDSTADDR)
		 dst_addr_4 = dst_addr.addr4 = *(p.a);
	       else if (cmptr->cmsg_level == IPPROTO_IP && cmptr->cmsg_type == IP_RECVIF)
#ifdef HAVE_SOLARIS_NETWORK
		 if_index = *(p.i);
#else
  	         if_index = p.s->sdl_index;
#endif
	    }
	}
#endif
      
      if (family == AF_INET6)
	{
	  for (cmptr = CMSG_FIRSTHDR(&msg); cmptr; cmptr = CMSG_NXTHDR(&msg, cmptr))
	    if (cmptr->cmsg_level == IPPROTO_IPV6 && cmptr->cmsg_type == daemon->v6pktinfo)
	      {
		union {
		  unsigned char *c;
		  struct in6_pktinfo *p;
		} p;
		p.c = CMSG_DATA(cmptr);
		  
		dst_addr.addr6 = p.p->ipi6_addr;
		if_index = p.p->ipi6_ifindex;
	      }
	}
      
      /* enforce available interface configuration */
      
      if (!indextoname(listen->fd, if_index, ifr.ifr_name))
	return;
      
      if (!iface_check(family, &dst_addr, ifr.ifr_name, &auth_dns))
	{
	   if (!option_bool(OPT_CLEVERBIND))
	     enumerate_interfaces(0); 
	   if (!loopback_exception(listen->fd, family, &dst_addr, ifr.ifr_name) &&
	       !label_exception(if_index, family, &dst_addr))
	     return;
	}

      if (family == AF_INET && option_bool(OPT_LOCALISE))
	{
	  struct irec *iface;
	  
	  /* get the netmask of the interface which has the address we were sent to.
	     This is no necessarily the interface we arrived on. */
	  
	  for (iface = daemon->interfaces; iface; iface = iface->next)
	    if (iface->addr.sa.sa_family == AF_INET &&
		iface->addr.in.sin_addr.s_addr == dst_addr_4.s_addr)
	      break;
	  
	  /* interface may be new */
	  if (!iface && !option_bool(OPT_CLEVERBIND))
	    enumerate_interfaces(0); 
	  
	  for (iface = daemon->interfaces; iface; iface = iface->next)
	    if (iface->addr.sa.sa_family == AF_INET &&
		iface->addr.in.sin_addr.s_addr == dst_addr_4.s_addr)
	      break;
	  
	  /* If we failed, abandon localisation */
	  if (iface)
	    netmask = iface->netmask;
	  else
	    dst_addr_4.s_addr = 0;
	}
    }
   
  /* log_query gets called indirectly all over the place, so 
     pass these in global variables - sorry. */
  daemon->log_display_id = ++daemon->log_id;
  daemon->log_source_addr = &source_addr;

#ifdef HAVE_DUMPFILE
  dump_packet(DUMP_QUERY, daemon->packet, (size_t)n, &source_addr, NULL);
#endif
  
#ifdef HAVE_CONNTRACK
  if (option_bool(OPT_CMARK_ALST_EN))
    have_mark = get_incoming_mark(&source_addr, &dst_addr, /* istcp: */ 0, &mark);
#endif
	  
  if (extract_request(header, (size_t)n, daemon->namebuff, &type))
    {
#ifdef HAVE_AUTH
      struct auth_zone *zone;
#endif
      char *types = querystr(auth_dns ? "auth" : "query", type);

      log_query_mysockaddr(F_QUERY | F_FORWARD, daemon->namebuff,
			   &source_addr, types);
      
#ifdef HAVE_CONNTRACK
      is_single_query = 1;
#endif

#ifdef HAVE_AUTH
      /* find queries for zones we're authoritative for, and answer them directly */
      if (!auth_dns && !option_bool(OPT_LOCALISE))
	for (zone = daemon->auth_zones; zone; zone = zone->next)
	  if (in_zone(zone, daemon->namebuff, NULL))
	    {
	      auth_dns = 1;
	      local_auth = 1;
	      break;
	    }
#endif
      
#ifdef HAVE_LOOP
      /* Check for forwarding loop */
      if (detect_loop(daemon->namebuff, type))
	return;
#endif
    }
  
  if (find_pseudoheader(header, (size_t)n, NULL, &pheader, NULL, NULL))
    { 
      unsigned short flags;
      
      have_pseudoheader = 1;
      GETSHORT(udp_size, pheader);
      pheader += 2; /* ext_rcode */
      GETSHORT(flags, pheader);
      
      if (flags & 0x8000)
	do_bit = 1;/* do bit */ 
	
      /* If the client provides an EDNS0 UDP size, use that to limit our reply.
	 (bounded by the maximum configured). If no EDNS0, then it
	 defaults to 512 */
      if (udp_size > daemon->edns_pktsz)
	udp_size = daemon->edns_pktsz;
      else if (udp_size < PACKETSZ)
	udp_size = PACKETSZ; /* Sanity check - can't reduce below default. RFC 6891 6.2.3 */
    }
#if 1  
	//printf("@!@dnsmasq %s len=%d\n",daemon->namebuff,strlen(daemon->namebuff));
	if(strlen(daemon->namebuff) == 0)//nessus DNS Server Spoofed Request Amplification DDoS
	  return;
#ifdef ZXIC_ONELINK_TEST
	//dnsmasq for onelink test
	{
		static char name_buf[512];
        char is_registerd[4] = {0};
        char lan_ipaddr[16] = {0};
        char lan_net_prefix[16] = {0};
        char *last_dot;

		memset(name_buf,0x00, sizeof(name_buf));
		cfg_get_item("LocalDomain", name_buf, sizeof(name_buf));
        cfg_get_item("is_registerd", is_registerd, sizeof(is_registerd));
        cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
        strncpy(lan_net_prefix, lan_ipaddr, sizeof(lan_net_prefix) - 1);
        last_dot = strrchr(lan_net_prefix, '.');
        if (last_dot) {
            *(last_dot + 1) = '\0';
        }
		printf("corem dnsmasq dns name:%s, type:%d(T_A:1, T_AAAA:28), local_name::%s is_registerd %s\n", daemon->namebuff, type, name_buf, is_registerd);

		if(!strstr(daemon->namebuff, "wireless.cmonelink.com") && !strstr(daemon->namebuff,name_buf) /*&& not registered*/&& strncmp(is_registerd, "1", 1) ) {
			// 打印IPv4和IPv6协议信息
			char src_ip_str[INET6_ADDRSTRLEN];
			char dst_ip_str[INET6_ADDRSTRLEN];
			int src_port = 0, dst_port = 0;
			int need_dns_redirect = 1; // 默认需要DNS重定向

			if (source_addr.sa.sa_family == AF_INET) {
				// IPv4协议
				inet_ntop(AF_INET, &source_addr.in.sin_addr, src_ip_str, INET_ADDRSTRLEN);
				inet_ntop(AF_INET, &dst_addr_4, dst_ip_str, INET_ADDRSTRLEN);
				src_port = ntohs(source_addr.in.sin_port);
				dst_port = 53; // DNS端口
				printf("corem dnsmasq IPv4 - interface:%s, type:%d(T_A:1, T_AAAA:28), src_ip:%s, src_port:%d, dst_ip:%s, dst_port:%d\n",
				       listen->iface ? listen->iface->name : "unknown", type, src_ip_str, src_port, dst_ip_str, dst_port);

				// 检查是否为动态网段的WiFi终端（排除网关地址）
				if (strncmp(src_ip_str, lan_net_prefix, strlen(lan_net_prefix)) == 0 && strcmp(src_ip_str, lan_ipaddr) != 0) {
					printf("corem dnsmasq WiFi terminal detected from %s network: %s\n", lan_net_prefix, src_ip_str);

					// 获取MAC地址
					unsigned char mac[DHCP_CHADDR_MAX];
					int maclen = find_mac(&source_addr, mac, 1, now);
					if (maclen > 0) {
						// 格式化MAC地址为xx:xx:xx:xx:xx:xx格式
						char mac_str[32] = {0};
						int i;
						for (i = 0; i < maclen; i++) {
							if (i > 0) strcat(mac_str, ":");
							sprintf(mac_str + strlen(mac_str), "%02x", mac[i]);
						}
						printf("corem dnsmasq resolved MAC address: %s\n", mac_str);

						// 检查MAC是否在认证列表中
						char one_link_authed_mac[1024] = {0};
						cfg_get_item("one_link_authed_mac", one_link_authed_mac, sizeof(one_link_authed_mac));

						if (strlen(one_link_authed_mac) > 0) {
							printf("corem dnsmasq one_link_authed_mac list: %s\n", one_link_authed_mac);

							// 直接在原始字符串中搜索MAC地址（格式：xx:xx:xx:xx:xx:xx;）
							char search_pattern[40] = {0};
							snprintf(search_pattern, sizeof(search_pattern), "%s;", mac_str);

							if (strstr(one_link_authed_mac, search_pattern) != NULL) {
								printf("corem dnsmasq MAC %s is authenticated, skip DNS redirect\n", mac_str);
								need_dns_redirect = 0; // 已认证，不需要DNS重定向

								// 清除该MAC的iptables限制规则，允许正常上网
								cleanup_authenticated_mac_rules(mac_str);
							} else {
								printf("corem dnsmasq MAC %s is NOT authenticated, need DNS redirect\n", mac_str);
							}
						} else {
							printf("corem dnsmasq no authenticated MAC list found, need DNS redirect\n");
						}
					} else {
						printf("corem dnsmasq failed to resolve MAC address for IP: %s\n", src_ip_str);
					}
				} else {
					// 不是WIFI连接用户DNS请求流量(MIFI设备内部流量)，不需要DNS重定向
					need_dns_redirect = 0;
				}
			} else if (source_addr.sa.sa_family == AF_INET6) {
				// IPv6协议 基本没有收到这种请求
				inet_ntop(AF_INET6, &source_addr.in6.sin6_addr, src_ip_str, INET6_ADDRSTRLEN);
				inet_ntop(AF_INET6, &dst_addr.addr6, dst_ip_str, INET6_ADDRSTRLEN);
				src_port = ntohs(source_addr.in6.sin6_port);
				dst_port = 53; // DNS端口
				printf("corem dnsmasq IPv6 - interface:%s, type:%d(T_A:1, T_AAAA:28), src_ip:%s, src_port:%d, dst_ip:%s, dst_port:%d\n",
				       listen->iface ? listen->iface->name : "unknown", type, src_ip_str, src_port, dst_ip_str, dst_port);
			}

			if(!need_dns_redirect) {
				printf("corem dnsmasq need_dns_redirect is 0, type:%d\n", type);
				goto normal;
			}

			// 不是DNS A和AAAA 记录
			if (type != T_A && type != T_AAAA)
			{
				plen = (size_t)n;

				setup_reply(daemon->packet, 0, 0);

				unsigned char *p = skip_questions(header, plen);

				if(p)

				{

					plen = p - (unsigned char *)header;

					//send_from(listen->fd, daemon->options & OPT_NOWILD, daemon->packet, plen, &source_addr, &dst_addr, if_index);

					send_from(listen->fd, option_bool(OPT_NOWILD), daemon->packet, plen, &source_addr, &dst_addr, if_index);

				}

				return;

			}



			if (type == T_A)
			{
				daemon->packet[2] = daemon->packet[2]|0x80;
				daemon->packet[7] = daemon->packet[7]|0x01;
				daemon->packet[num] = 0xc0;
				daemon->packet[num+1] = 0x0c;
				daemon->packet[num+2] = 0x00;
				daemon->packet[num+3] = 0x01;
				daemon->packet[num+4] = 0x00;
				daemon->packet[num+5] = 0x01;
				daemon->packet[num+6] = 0x00;
				daemon->packet[num+7] = 0x00;
				daemon->packet[num+8] = 0x00;
				daemon->packet[num+9] = 0x0a;
				daemon->packet[num+10] = 0x00;
				daemon->packet[num+11] = 0x04;
				daemon->packet[num+12] = *((char *)(&(dst_addr.addr4)) + 0)& 0xff;
				daemon->packet[num+13] = *((char *)(&(dst_addr.addr4)) + 1)& 0xff;
				daemon->packet[num+14] = *((char *)(&(dst_addr.addr4)) + 2)& 0xff;
				daemon->packet[num+15] = *((char *)(&(dst_addr.addr4)) + 3)& 0xff;
				
				//send_from(listen->fd, daemon->options & OPT_NOWILD, daemon->packet, num+16, &source_addr, &dst_addr, if_index);
				// A记录DNS 返回网关IP给终端用户，AAAA记录返回可能有问题(需要抓包)
				send_from(listen->fd, option_bool(OPT_NOWILD), daemon->packet, num+16, &source_addr, &dst_addr, if_index);
			}
			else if (type == T_AAAA)
			{
				daemon->packet[2] = daemon->packet[2]|0x80;
				daemon->packet[7] = daemon->packet[7]|0x01;
				daemon->packet[num] = 0xc0;
				daemon->packet[num+1] = 0x0c;
				daemon->packet[num+2] = 0x00;
				daemon->packet[num+3] = 0x1c; // T_AAAA
				daemon->packet[num+4] = 0x00;
				daemon->packet[num+5] = 0x01;
				daemon->packet[num+6] = 0x00;
				daemon->packet[num+7] = 0x00;
				daemon->packet[num+8] = 0x00;
				daemon->packet[num+9] = 0x0a;
				daemon->packet[num+10] = 0x00;
				daemon->packet[num+11] = 0x10; // 16 bytes for IPv6
				
				struct irec *iface;
				int found_ipv6 = 0;
				if (listen->iface && listen->iface->name)
				{
					for (iface = daemon->interfaces; iface; iface = iface->next)
					{
						if (iface->addr.sa.sa_family == AF_INET6 && strcmp(iface->name, listen->iface->name) == 0)
						{
							char ipv6_addr_str[INET6_ADDRSTRLEN];
							inet_ntop(AF_INET6, &iface->addr.in6.sin6_addr, ipv6_addr_str, sizeof(ipv6_addr_str));
							printf("corem dnsmasq found IPv6 address from interface %s: %s\n", iface->name, ipv6_addr_str);
							memcpy(&daemon->packet[num+12], &iface->addr.in6.sin6_addr, 16);
							found_ipv6 = 1;
							break;
						}
					}
				}

				if (!found_ipv6)
				{
					char ipv6_addr_str[INET6_ADDRSTRLEN] = {0};
					cfg_get_item("ipv6_lan_ipaddr", ipv6_addr_str, sizeof(ipv6_addr_str));
					if (strlen(ipv6_addr_str) > 0)
					{
						struct in6_addr ipv6_addr;
						if (inet_pton(AF_INET6, ipv6_addr_str, &ipv6_addr) == 1)
						{
							printf("corem dnsmasq using IPv6 address from config: %s\n", ipv6_addr_str);
							memcpy(&daemon->packet[num+12], &ipv6_addr, 16);
							found_ipv6 = 1;
						}
					}
				}

				if(found_ipv6)
				{
					send_from(listen->fd, option_bool(OPT_NOWILD), daemon->packet, num+12+16, &source_addr, &dst_addr, if_index);
				}
				else
				{
					printf("corem dnsmasq no IPv6 address found for T_AAAA redirect\n");
				}
			}

			printf("corem dnsmasq dns redirct name:%s\n", daemon->namebuff);

			return;

		}

	}
normal:
	;
#endif
	{
	char cfg_buf[32];
	memset(cfg_buf, 0x00, sizeof(cfg_buf));
	cfg_get_item("lan_domain_Enabled", cfg_buf, sizeof(cfg_buf));
	if(0 == strcmp(cfg_buf, "1"))
	{
		static char name_buf[512];
		memset(name_buf,0x00, sizeof(name_buf));
		cfg_get_item("LocalDomain", name_buf, sizeof(name_buf));
		if(!strcmp(daemon->namebuff,name_buf))  
		{

			if (type != T_A)
			{
				plen = (size_t)n;
				setup_reply(daemon->packet, 0, 0);
				unsigned char *p = skip_questions(header, plen);
				if(p)
				{
					plen = p - (unsigned char *)header;
					//send_from(listen->fd, daemon->options & OPT_NOWILD, daemon->packet, plen, &source_addr, &dst_addr, if_index);
					send_from(listen->fd, option_bool(OPT_NOWILD), daemon->packet, plen, &source_addr, &dst_addr, if_index);
				}
				return;
			}

			daemon->packet[2] = daemon->packet[2]|0x80;
			daemon->packet[7] = daemon->packet[7]|0x01;
			daemon->packet[num] = 0xc0;
			daemon->packet[num+1] = 0x0c;
			daemon->packet[num+2] = 0x00;
			daemon->packet[num+3] = 0x01;
			daemon->packet[num+4] = 0x00;
			daemon->packet[num+5] = 0x01;
			daemon->packet[num+6] = 0x00;
			daemon->packet[num+7] = 0x00;
			daemon->packet[num+8] = 0x00;
			daemon->packet[num+9] = 0x0a;
			daemon->packet[num+10] = 0x00;
			daemon->packet[num+11] = 0x04;
			daemon->packet[num+12] = *((char *)(&(dst_addr.addr4)) + 0)& 0xff;
			daemon->packet[num+13] = *((char *)(&(dst_addr.addr4)) + 1)& 0xff;
			daemon->packet[num+14] = *((char *)(&(dst_addr.addr4)) + 2)& 0xff;
			daemon->packet[num+15] = *((char *)(&(dst_addr.addr4)) + 3)& 0xff;
			//send_from(listen->fd, daemon->options & OPT_NOWILD, daemon->packet, num+16, &source_addr, &dst_addr, if_index);
			send_from(listen->fd, option_bool(OPT_NOWILD), daemon->packet, num+16, &source_addr, &dst_addr, if_index);
			return;
		}
	}
	{
	char web_buf[16] = {0};
	cfg_get_item("DNS_proxy", web_buf, sizeof(web_buf));
	if(0 == strcmp(web_buf, "disable"))
	{
		return;
	}
	}
	}
#endif
#ifdef HAVE_CONNTRACK
#ifdef HAVE_AUTH
  if (!auth_dns || local_auth)
#endif
    if (option_bool(OPT_CMARK_ALST_EN) && have_mark && ((u32)mark & daemon->allowlist_mask))
      allowed = is_query_allowed_for_mark((u32)mark, is_single_query ? daemon->namebuff : NULL);
#endif
  
  if (0);
#ifdef HAVE_CONNTRACK
  else if (!allowed)
    {
      u16 swap = htons(EDE_BLOCKED);

      m = answer_disallowed(header, (size_t)n, (u32)mark, is_single_query ? daemon->namebuff : NULL);
      
      if (have_pseudoheader && m != 0)
	m = add_pseudoheader(header,  m,  ((unsigned char *) header) + udp_size, daemon->edns_pktsz,
			     EDNS0_OPTION_EDE, (unsigned char *)&swap, 2, do_bit, 0);
      
      if (m >= 1)
	{
#ifdef HAVE_DUMPFILE
	  dump_packet(DUMP_REPLY, daemon->packet, m, NULL, &source_addr);
#endif
	  send_from(listen->fd, option_bool(OPT_NOWILD) || option_bool(OPT_CLEVERBIND),
		    (char *)header, m, &source_addr, &dst_addr, if_index);
	  daemon->metrics[METRIC_DNS_LOCAL_ANSWERED]++;
	}
    }
#endif
#ifdef HAVE_AUTH
  else if (auth_dns)
    {
      m = answer_auth(header, ((char *) header) + udp_size, (size_t)n, now, &source_addr, 
		      local_auth, do_bit, have_pseudoheader);
      if (m >= 1)
	{
#ifdef HAVE_DUMPFILE
	  dump_packet(DUMP_REPLY, daemon->packet, m, NULL, &source_addr);
#endif
#if defined(HAVE_CONNTRACK) && defined(HAVE_UBUS)
	  if (local_auth)
	    if (option_bool(OPT_CMARK_ALST_EN) && have_mark && ((u32)mark & daemon->allowlist_mask))
	      report_addresses(header, m, mark);
#endif
	  send_from(listen->fd, option_bool(OPT_NOWILD) || option_bool(OPT_CLEVERBIND),
		    (char *)header, m, &source_addr, &dst_addr, if_index);
	  daemon->metrics[METRIC_DNS_AUTH_ANSWERED]++;
	}
    }
#endif
  else
    {
      int ad_reqd = do_bit;
      /* RFC 6840 5.7 */
      if (header->hb4 & HB4_AD)
	ad_reqd = 1;
      
      m = answer_request(header, ((char *) header) + udp_size, (size_t)n, 
			 dst_addr_4, netmask, now, ad_reqd, do_bit, have_pseudoheader);
      
      if (m >= 1)
	{
#ifdef HAVE_DUMPFILE
	  dump_packet(DUMP_REPLY, daemon->packet, m, NULL, &source_addr);
#endif
#if defined(HAVE_CONNTRACK) && defined(HAVE_UBUS)
	  if (option_bool(OPT_CMARK_ALST_EN) && have_mark && ((u32)mark & daemon->allowlist_mask))
	    report_addresses(header, m, mark);
#endif
	  send_from(listen->fd, option_bool(OPT_NOWILD) || option_bool(OPT_CLEVERBIND),
		    (char *)header, m, &source_addr, &dst_addr, if_index);
	  daemon->metrics[METRIC_DNS_LOCAL_ANSWERED]++;
	}
      else if (forward_query(listen->fd, &source_addr, &dst_addr, if_index,
			     header, (size_t)n,  ((char *) header) + udp_size, now, NULL, ad_reqd, do_bit))
	daemon->metrics[METRIC_DNS_QUERIES_FORWARDED]++;
      else
	daemon->metrics[METRIC_DNS_LOCAL_ANSWERED]++;
    }
}

/* Send query in packet, qsize to a server determined by first,last,start and
   get the reply. return reply size. */
static ssize_t tcp_talk(int first, int last, int start, unsigned char *packet,  size_t qsize,
			int have_mark, unsigned int mark, struct server **servp)
{
  int firstsendto = -1;
  u16 *length = (u16 *)packet;
  unsigned char *payload = &packet[2];
  struct dns_header *header = (struct dns_header *)payload;
  unsigned char c1, c2;
  unsigned char hash[HASH_SIZE];
  unsigned int rsize;
  
  (void)mark;
  (void)have_mark;

  memcpy(hash, hash_questions(header, (unsigned int)qsize, daemon->namebuff), HASH_SIZE);
  
  while (1) 
    {
      int data_sent = 0;
      struct server *serv;
      
      if (firstsendto == -1)
	firstsendto = start;
      else
	{
	  start++;
	  
	  if (start == last)
	    start = first;
	  
	  if (start == firstsendto)
	    break;
	}
      
      serv = daemon->serverarray[start];
      
    retry:
      *length = htons(qsize);
      
      if (serv->tcpfd == -1)
	{
	  if ((serv->tcpfd = socket(serv->addr.sa.sa_family, SOCK_STREAM, 0)) == -1)
	    continue;
	  
#ifdef HAVE_CONNTRACK
	  /* Copy connection mark of incoming query to outgoing connection. */
	  if (have_mark)
	    setsockopt(serv->tcpfd, SOL_SOCKET, SO_MARK, &mark, sizeof(unsigned int));
#endif			  
	  
	  if ((!local_bind(serv->tcpfd,  &serv->source_addr, serv->interface, 0, 1)))
	    {
	      close(serv->tcpfd);
	      serv->tcpfd = -1;
	      continue;
	    }
	  
#ifdef MSG_FASTOPEN
	  server_send(serv, serv->tcpfd, packet, qsize + sizeof(u16), MSG_FASTOPEN);
	  
	  if (errno == 0)
	    data_sent = 1;
#endif
	  
	  if (!data_sent && connect(serv->tcpfd, &serv->addr.sa, sa_len(&serv->addr)) == -1)
	    {
	      close(serv->tcpfd);
	      serv->tcpfd = -1;
	      continue;
	    }
	  
	  daemon->serverarray[first]->last_server = start;
	  serv->flags &= ~SERV_GOT_TCP;
	}
      
      if ((!data_sent && !read_write(serv->tcpfd, packet, qsize + sizeof(u16), 0)) ||
	  !read_write(serv->tcpfd, &c1, 1, 1) ||
	  !read_write(serv->tcpfd, &c2, 1, 1) ||
	  !read_write(serv->tcpfd, payload, (rsize = (c1 << 8) | c2), 1))
	{
	  close(serv->tcpfd);
	  serv->tcpfd = -1;
	  /* We get data then EOF, reopen connection to same server,
	     else try next. This avoids DoS from a server which accepts
	     connections and then closes them. */
	  if (serv->flags & SERV_GOT_TCP)
	    goto retry;
	  else
	    continue;
	}

      /* If the hash of the question section doesn't match the crc we sent, then
	 someone might be attempting to insert bogus values into the cache by 
	 sending replies containing questions and bogus answers. 
	 Try another server, or give up */
      if (memcmp(hash, hash_questions(header, rsize, daemon->namebuff), HASH_SIZE) != 0)
	continue;
      
      serv->flags |= SERV_GOT_TCP;
      
      *servp = serv;
      return rsize;
    }

  return 0;
}
		  
#ifdef HAVE_DNSSEC
/* Recurse down the key hierarchy */
static int tcp_key_recurse(time_t now, int status, struct dns_header *header, size_t n, 
			   int class, char *name, char *keyname, struct server *server, 
			   int have_mark, unsigned int mark, int *keycount)
{
  int first, last, start, new_status;
  unsigned char *packet = NULL;
  struct dns_header *new_header = NULL;
  
  while (1)
    {
      size_t m;
      int log_save;
            
      /* limit the amount of work we do, to avoid cycling forever on loops in the DNS */
      if (--(*keycount) == 0)
	new_status = STAT_ABANDONED;
      else if (STAT_ISEQUAL(status, STAT_NEED_KEY))
	new_status = dnssec_validate_by_ds(now, header, n, name, keyname, class);
      else if (STAT_ISEQUAL(status, STAT_NEED_DS))
	new_status = dnssec_validate_ds(now, header, n, name, keyname, class);
      else 
	new_status = dnssec_validate_reply(now, header, n, name, keyname, &class,
					   !option_bool(OPT_DNSSEC_IGN_NS) && (server->flags & SERV_DO_DNSSEC),
					   NULL, NULL, NULL);
      
      if (!STAT_ISEQUAL(new_status, STAT_NEED_DS) && !STAT_ISEQUAL(new_status, STAT_NEED_KEY))
	break;

      /* Can't validate because we need a key/DS whose name now in keyname.
	 Make query for same, and recurse to validate */
      if (!packet)
	{
	  packet = whine_malloc(65536 + MAXDNAME + RRFIXEDSZ + sizeof(u16));
	  new_header = (struct dns_header *)&packet[2];
	}
      
      if (!packet)
	{
	  new_status = STAT_ABANDONED;
	  break;
	}

      m = dnssec_generate_query(new_header, ((unsigned char *) new_header) + 65536, keyname, class, 
				STAT_ISEQUAL(new_status, STAT_NEED_KEY) ? T_DNSKEY : T_DS, server->edns_pktsz);
      
      if ((start = dnssec_server(server, daemon->keyname, &first, &last)) == -1 ||
	  (m = tcp_talk(first, last, start, packet, m, have_mark, mark, &server)) == 0)
	{
	  new_status = STAT_ABANDONED;
	  break;
	}

      log_save = daemon->log_display_id;
      daemon->log_display_id = ++daemon->log_id;
      
      log_query_mysockaddr(F_NOEXTRA | F_DNSSEC, keyname, &server->addr,
			   querystr("dnssec-query", STAT_ISEQUAL(new_status, STAT_NEED_KEY) ? T_DNSKEY : T_DS));
            
      new_status = tcp_key_recurse(now, new_status, new_header, m, class, name, keyname, server, have_mark, mark, keycount);

      daemon->log_display_id = log_save;
      
      if (!STAT_ISEQUAL(new_status, STAT_OK))
	break;
    }
    
  if (packet)
    free(packet);
    
  return new_status;
}
#endif


/* The daemon forks before calling this: it should deal with one connection,
   blocking as necessary, and then return. Note, need to be a bit careful
   about resources for debug mode, when the fork is suppressed: that's
   done by the caller. */
unsigned char *tcp_request(int confd, time_t now,
			   union mysockaddr *local_addr, struct in_addr netmask, int auth_dns)
{
  size_t size = 0;
  int norebind;
#ifdef HAVE_CONNTRACK
  int is_single_query = 0, allowed = 1;
#endif
#ifdef HAVE_AUTH
  int local_auth = 0;
#endif
  int checking_disabled, do_bit, added_pheader = 0, have_pseudoheader = 0;
  int check_subnet, cacheable, no_cache_dnssec = 0, cache_secure = 0, bogusanswer = 0;
  size_t m;
  unsigned short qtype;
  unsigned int gotname;
  /* Max TCP packet + slop + size */
  unsigned char *packet = whine_malloc(65536 + MAXDNAME + RRFIXEDSZ + sizeof(u16));
  unsigned char *payload = &packet[2];
  unsigned char c1, c2;
  /* largest field in header is 16-bits, so this is still sufficiently aligned */
  struct dns_header *header = (struct dns_header *)payload;
  u16 *length = (u16 *)packet;
  struct server *serv;
  struct in_addr dst_addr_4;
  union mysockaddr peer_addr;
  socklen_t peer_len = sizeof(union mysockaddr);
  int query_count = 0;
  unsigned char *pheader;
  unsigned int mark = 0;
  int have_mark = 0;
  int first, last;
  unsigned int flags = 0;
    
  if (getpeername(confd, (struct sockaddr *)&peer_addr, &peer_len) == -1)
    return packet;

#ifdef HAVE_CONNTRACK
  /* Get connection mark of incoming query to set on outgoing connections. */
  if (option_bool(OPT_CONNTRACK) || option_bool(OPT_CMARK_ALST_EN))
    {
      union all_addr local;
		      
      if (local_addr->sa.sa_family == AF_INET6)
	local.addr6 = local_addr->in6.sin6_addr;
      else
	local.addr4 = local_addr->in.sin_addr;
      
      have_mark = get_incoming_mark(&peer_addr, &local, 1, &mark);
    }
#endif	

  /* We can be configured to only accept queries from at-most-one-hop-away addresses. */
  if (option_bool(OPT_LOCAL_SERVICE))
    {
      struct addrlist *addr;

      if (peer_addr.sa.sa_family == AF_INET6) 
	{
	  for (addr = daemon->interface_addrs; addr; addr = addr->next)
	    if ((addr->flags & ADDRLIST_IPV6) &&
		is_same_net6(&addr->addr.addr6, &peer_addr.in6.sin6_addr, addr->prefixlen))
	      break;
	}
      else
	{
	  struct in_addr netmask;
	  for (addr = daemon->interface_addrs; addr; addr = addr->next)
	    {
	      netmask.s_addr = htonl(~(in_addr_t)0 << (32 - addr->prefixlen));
	      if (!(addr->flags & ADDRLIST_IPV6) && 
		  is_same_net(addr->addr.addr4, peer_addr.in.sin_addr, netmask))
		break;
	    }
	}
      if (!addr)
	{
	  my_syslog(LOG_WARNING, _("Ignoring query from non-local network"));
	  return packet;
	}
    }

  while (1)
    {
      int ede = EDE_UNSET;

      if (query_count == TCP_MAX_QUERIES ||
	  !packet ||
	  !read_write(confd, &c1, 1, 1) || !read_write(confd, &c2, 1, 1) ||
	  !(size = c1 << 8 | c2) ||
	  !read_write(confd, payload, size, 1))
       	return packet; 
  
      if (size < (int)sizeof(struct dns_header))
	continue;

      /* Clear buffer beyond request to avoid risk of
	 information disclosure. */
      memset(payload + size, 0, 65536 - size);
      
      query_count++;

      /* log_query gets called indirectly all over the place, so 
	 pass these in global variables - sorry. */
      daemon->log_display_id = ++daemon->log_id;
      daemon->log_source_addr = &peer_addr;
      
      /* save state of "cd" flag in query */
      if ((checking_disabled = header->hb4 & HB4_CD))
	no_cache_dnssec = 1;
       
      if ((gotname = extract_request(header, (unsigned int)size, daemon->namebuff, &qtype)))
	{
#ifdef HAVE_AUTH
	  struct auth_zone *zone;
#endif
	  char *types = querystr(auth_dns ? "auth" : "query", qtype);
	  
	  log_query_mysockaddr(F_QUERY | F_FORWARD, daemon->namebuff,
			       &peer_addr, types);
	  
#ifdef HAVE_CONNTRACK
	  is_single_query = 1;
#endif
	  
#ifdef HAVE_AUTH
	  /* find queries for zones we're authoritative for, and answer them directly */
	  if (!auth_dns && !option_bool(OPT_LOCALISE))
	    for (zone = daemon->auth_zones; zone; zone = zone->next)
	      if (in_zone(zone, daemon->namebuff, NULL))
		{
		  auth_dns = 1;
		  local_auth = 1;
		  break;
		}
#endif
	}
      
      norebind = domain_no_rebind(daemon->namebuff);
      
      if (local_addr->sa.sa_family == AF_INET)
	dst_addr_4 = local_addr->in.sin_addr;
      else
	dst_addr_4.s_addr = 0;
      
      do_bit = 0;

      if (find_pseudoheader(header, (size_t)size, NULL, &pheader, NULL, NULL))
	{ 
	  unsigned short flags;
	  
	  have_pseudoheader = 1;
	  pheader += 4; /* udp_size, ext_rcode */
	  GETSHORT(flags, pheader);
      
	  if (flags & 0x8000)
	    do_bit = 1; /* do bit */ 
	}
      
#ifdef HAVE_CONNTRACK
#ifdef HAVE_AUTH
      if (!auth_dns || local_auth)
#endif
	if (option_bool(OPT_CMARK_ALST_EN) && have_mark && ((u32)mark & daemon->allowlist_mask))
	  allowed = is_query_allowed_for_mark((u32)mark, is_single_query ? daemon->namebuff : NULL);
#endif

      if (0);
#ifdef HAVE_CONNTRACK
      else if (!allowed)
	{
	  u16 swap = htons(EDE_BLOCKED);

	  m = answer_disallowed(header, size, (u32)mark, is_single_query ? daemon->namebuff : NULL);
	  
	  if (have_pseudoheader && m != 0)
	    m = add_pseudoheader(header,  m, ((unsigned char *) header) + 65536, daemon->edns_pktsz,
				 EDNS0_OPTION_EDE, (unsigned char *)&swap, 2, do_bit, 0);
	}
#endif
#ifdef HAVE_AUTH
      else if (auth_dns)
	m = answer_auth(header, ((char *) header) + 65536, (size_t)size, now, &peer_addr, 
			local_auth, do_bit, have_pseudoheader);
#endif
      else
	{
	   int ad_reqd = do_bit;
	   /* RFC 6840 5.7 */
	   if (header->hb4 & HB4_AD)
	     ad_reqd = 1;
	   
	   /* m > 0 if answered from cache */
	   m = answer_request(header, ((char *) header) + 65536, (size_t)size, 
			      dst_addr_4, netmask, now, ad_reqd, do_bit, have_pseudoheader);
	  
	  /* Do this by steam now we're not in the select() loop */
	  check_log_writer(1); 
	  
	  if (m == 0)
	    {
	      struct server *master;
	      int start;

	      if (lookup_domain(daemon->namebuff, gotname, &first, &last))
		flags = is_local_answer(now, first, daemon->namebuff);
	      else
		{
		  /* No configured servers */
		  ede = EDE_NOT_READY;
		  flags = 0;
		}
	      
	      /* don't forward A or AAAA queries for simple names, except the empty name */
	      if (!flags &&
		  option_bool(OPT_NODOTS_LOCAL) &&
		  (gotname & (F_IPV4 | F_IPV6)) &&
		  !strchr(daemon->namebuff, '.') &&
		  strlen(daemon->namebuff) != 0)
		flags = check_for_local_domain(daemon->namebuff, now) ? F_NOERR : F_NXDOMAIN;
		
	      if (!flags && ede != EDE_NOT_READY)
		{
		  master = daemon->serverarray[first];
		  
		  if (option_bool(OPT_ORDER) || master->last_server == -1)
		    start = first;
		  else
		    start = master->last_server;
		  
		  size = add_edns0_config(header, size, ((unsigned char *) header) + 65536, &peer_addr, now, &check_subnet, &cacheable);
		  
#ifdef HAVE_DNSSEC
		  if (option_bool(OPT_DNSSEC_VALID) && (master->flags & SERV_DO_DNSSEC))
		    {
		      size = add_do_bit(header, size, ((unsigned char *) header) + 65536);
		      
		      /* For debugging, set Checking Disabled, otherwise, have the upstream check too,
			 this allows it to select auth servers when one is returning bad data. */
		      if (option_bool(OPT_DNSSEC_DEBUG))
			header->hb4 |= HB4_CD;
		    }
#endif
		  
		  /* Check if we added a pheader on forwarding - may need to
		     strip it from the reply. */
		  if (!have_pseudoheader && find_pseudoheader(header, size, NULL, NULL, NULL, NULL))
		    added_pheader = 1;
		  
		  /* Loop round available servers until we succeed in connecting to one. */
		  if ((m = tcp_talk(first, last, start, packet, size, have_mark, mark, &serv)) == 0)
		    {
		      ede = EDE_NETERR;
		      break;
		    }
		  
		  /* get query name again for logging - may have been overwritten */
		  if (!(gotname = extract_request(header, (unsigned int)size, daemon->namebuff, &qtype)))
		    strcpy(daemon->namebuff, "query");
		  log_query_mysockaddr(F_SERVER | F_FORWARD, daemon->namebuff, &serv->addr, NULL);
		  
#ifdef HAVE_DNSSEC
		  if (option_bool(OPT_DNSSEC_VALID) && !checking_disabled && (master->flags & SERV_DO_DNSSEC))
		    {
		      int keycount = DNSSEC_WORK; /* Limit to number of DNSSEC questions, to catch loops and avoid filling cache. */
		      int status = tcp_key_recurse(now, STAT_OK, header, m, 0, daemon->namebuff, daemon->keyname, 
						   serv, have_mark, mark, &keycount);
		      char *result, *domain = "result";
		      
		      union all_addr a;
		      a.log.ede = ede = errflags_to_ede(status);
		      
		      if (STAT_ISEQUAL(status, STAT_ABANDONED))
			{
			  result = "ABANDONED";
			  status = STAT_BOGUS;
			}
		      else
			result = (STAT_ISEQUAL(status, STAT_SECURE) ? "SECURE" : (STAT_ISEQUAL(status, STAT_INSECURE) ? "INSECURE" : "BOGUS"));
		      
		      if (STAT_ISEQUAL(status, STAT_SECURE))
			cache_secure = 1;
		      else if (STAT_ISEQUAL(status, STAT_BOGUS))
			{
			  no_cache_dnssec = 1;
			  bogusanswer = 1;
			  
			  if (extract_request(header, m, daemon->namebuff, NULL))
			    domain = daemon->namebuff;
			}
		      
		      log_query(F_SECSTAT, domain, &a, result);
		    }
#endif
		  
		  /* restore CD bit to the value in the query */
		  if (checking_disabled)
		    header->hb4 |= HB4_CD;
		  else
		    header->hb4 &= ~HB4_CD;
		  
		  /* Never cache answers which are contingent on the source or MAC address EDSN0 option,
		     since the cache is ignorant of such things. */
		  if (!cacheable)
		    no_cache_dnssec = 1;
		  
		  m = process_reply(header, now, serv, (unsigned int)m, 
				    option_bool(OPT_NO_REBIND) && !norebind, no_cache_dnssec, cache_secure, bogusanswer,
				    ad_reqd, do_bit, added_pheader, check_subnet, &peer_addr, ((unsigned char *)header) + 65536, ede); 
		}
	    }
	}
	
      /* In case of local answer or no connections made. */
      if (m == 0)
	{
	  if (!(m = make_local_answer(flags, gotname, size, header, daemon->namebuff,
				      ((char *) header) + 65536, first, last, ede)))
	    break;
	  
	  if (have_pseudoheader)
	    {
	      u16 swap = htons((u16)ede);

	       if (ede != EDE_UNSET)
		 m = add_pseudoheader(header, m, ((unsigned char *) header) + 65536, daemon->edns_pktsz, EDNS0_OPTION_EDE, (unsigned char *)&swap, 2, do_bit, 0);
	       else
		 m = add_pseudoheader(header, m, ((unsigned char *) header) + 65536, daemon->edns_pktsz, 0, NULL, 0, do_bit, 0);
	    }
	}
      
      check_log_writer(1);
      
      *length = htons(m);
      
#if defined(HAVE_CONNTRACK) && defined(HAVE_UBUS)
#ifdef HAVE_AUTH
      if (!auth_dns || local_auth)
#endif
	if (option_bool(OPT_CMARK_ALST_EN) && have_mark && ((u32)mark & daemon->allowlist_mask))
	  report_addresses(header, m, mark);
#endif
      if (!read_write(confd, packet, m + sizeof(u16), 0))
	break;
    }
  
  return packet;
}

/* return a UDP socket bound to a random port, have to cope with straying into
   occupied port nos and reserved ones. */
static int random_sock(struct server *s)
{
  int fd;

  if ((fd = socket(s->source_addr.sa.sa_family, SOCK_DGRAM, 0)) != -1)
    {
      if (local_bind(fd, &s->source_addr, s->interface, s->ifindex, 0))
	return fd;

      if (s->interface[0] == 0)
	(void)prettyprint_addr(&s->source_addr, daemon->namebuff);
      else
	strcpy(daemon->namebuff, s->interface);

      my_syslog(LOG_ERR, _("failed to bind server socket to %s: %s"),
		daemon->namebuff, strerror(errno));
      close(fd);
    }
  
  return -1;
}

/* compare source addresses and interface, serv2 can be null. */
static int server_isequal(const struct server *serv1,
			 const struct server *serv2)
{
  return (serv2 &&
    serv2->ifindex == serv1->ifindex &&
    sockaddr_isequal(&serv2->source_addr, &serv1->source_addr) &&
    strncmp(serv2->interface, serv1->interface, IF_NAMESIZE) == 0);
}

/* fdlp points to chain of randomfds already in use by transaction.
   If there's already a suitable one, return it, else allocate a 
   new one and add it to the list. 

   Not leaking any resources in the face of allocation failures
   is rather convoluted here.
   
   Note that rfd->serv may be NULL, when a server goes away.
*/
int allocate_rfd(struct randfd_list **fdlp, struct server *serv)
{
  static int finger = 0;
  int i, j = 0;
  struct randfd_list *rfl;
  struct randfd *rfd = NULL;
  int fd = 0;
  
  /* If server has a pre-allocated fd, use that. */
  if (serv->sfd)
    return serv->sfd->fd;
  
  /* existing suitable random port socket linked to this transaction? */
  for (rfl = *fdlp; rfl; rfl = rfl->next)
    if (server_isequal(serv, rfl->rfd->serv))
      return rfl->rfd->fd;

  /* No. need new link. */
  if ((rfl = daemon->rfl_spare))
    daemon->rfl_spare = rfl->next;
  else if (!(rfl = whine_malloc(sizeof(struct randfd_list))))
    return -1;
   
  /* limit the number of sockets we have open to avoid starvation of 
     (eg) TFTP. Once we have a reasonable number, randomness should be OK */
  for (i = 0; i < daemon->numrrand; i++)
    if (daemon->randomsocks[i].refcount == 0)
      {
	if ((fd = random_sock(serv)) != -1)
    	  {
	    rfd = &daemon->randomsocks[i];
	    rfd->serv = serv;
	    rfd->fd = fd;
	    rfd->refcount = 1;
	  }
	break;
      }
  
  /* No free ones or cannot get new socket, grab an existing one */
  if (!rfd)
    for (j = 0; j < daemon->numrrand; j++)
      {
	i = (j + finger) % daemon->numrrand;
	if (daemon->randomsocks[i].refcount != 0 &&
	    server_isequal(serv, daemon->randomsocks[i].serv) &&
	    daemon->randomsocks[i].refcount != 0xfffe)
	  {
	    finger = i + 1;
	    rfd = &daemon->randomsocks[i];
	    rfd->refcount++;
	    break;
	  }
      }

  if (j == daemon->numrrand)
    {
      struct randfd_list *rfl_poll;

      /* there are no free slots, and non with the same parameters we can piggy-back on. 
	 We're going to have to allocate a new temporary record, distinguished by
	 refcount == 0xffff. This will exist in the frec randfd list, never be shared,
	 and be freed when no longer in use. It will also be held on 
	 the daemon->rfl_poll list so the poll system can find it. */

      if ((rfl_poll = daemon->rfl_spare))
	daemon->rfl_spare = rfl_poll->next;
      else
	rfl_poll = whine_malloc(sizeof(struct randfd_list));
      
      if (!rfl_poll ||
	  !(rfd = whine_malloc(sizeof(struct randfd))) ||
	  (fd = random_sock(serv)) == -1)
	{
	  
	  /* Don't leak anything we may already have */
	  rfl->next = daemon->rfl_spare;
	  daemon->rfl_spare = rfl;

	  if (rfl_poll)
	    {
	      rfl_poll->next = daemon->rfl_spare;
	      daemon->rfl_spare = rfl_poll;
	    }
	  
	  if (rfd)
	    free(rfd);
	  
	  return -1; /* doom */
	}

      /* Note rfd->serv not set here, since it's not reused */
      rfd->fd = fd;
      rfd->refcount = 0xffff; /* marker for temp record */

      rfl_poll->rfd = rfd;
      rfl_poll->next = daemon->rfl_poll;
      daemon->rfl_poll = rfl_poll;
    }
  
  rfl->rfd = rfd;
  rfl->next = *fdlp;
  *fdlp = rfl;
  
  return rfl->rfd->fd;
}

void free_rfds(struct randfd_list **fdlp)
{
  struct randfd_list *tmp, *rfl, *poll, *next, **up;
  
  for (rfl = *fdlp; rfl; rfl = tmp)
    {
      if (rfl->rfd->refcount == 0xffff || --(rfl->rfd->refcount) == 0)
	close(rfl->rfd->fd);

      /* temporary overflow record */
      if (rfl->rfd->refcount == 0xffff)
	{
	  free(rfl->rfd);
	  
	  /* go through the link of all these by steam to delete.
	     This list is expected to be almost always empty. */
	  for (poll = daemon->rfl_poll, up = &daemon->rfl_poll; poll; poll = next)
	    {
	      next = poll->next;
	      
	      if (poll->rfd == rfl->rfd)
		{
		  *up = poll->next;
		  poll->next = daemon->rfl_spare;
		  daemon->rfl_spare = poll;
		}
	      else
		up = &poll->next;
	    }
	}

      tmp = rfl->next;
      rfl->next = daemon->rfl_spare;
      daemon->rfl_spare = rfl;
    }

  *fdlp = NULL;
}

static void free_frec(struct frec *f)
{
  struct frec_src *last;
  
  /* add back to freelist if not the record builtin to every frec. */
  for (last = f->frec_src.next; last && last->next; last = last->next) ;
  if (last)
    {
      last->next = daemon->free_frec_src;
      daemon->free_frec_src = f->frec_src.next;
    }
    
  f->frec_src.next = NULL;    
  free_rfds(&f->rfds);
  f->sentto = NULL;
  f->flags = 0;

#ifdef HAVE_DNSSEC
  if (f->stash)
    {
      blockdata_free(f->stash);
      f->stash = NULL;
    }

  /* Anything we're waiting on is pointless now, too */
  if (f->blocking_query)
    {
      struct frec *n, **up;

      /* unlink outselves from the blocking query's dependents list. */
      for (n = f->blocking_query->dependent, up = &f->blocking_query->dependent; n; n = n->next_dependent)
	if (n == f)
	  {
	    *up = n->next_dependent;
	    break;
	  }
	else
	  up = &n->next_dependent;

      /* If we were the only/last dependent, free the blocking query too. */
      if (!f->blocking_query->dependent)
	free_frec(f->blocking_query);
    }
  
  f->blocking_query = NULL;
  f->dependent = NULL;
  f->next_dependent = NULL;
#endif
}



/* Impose an absolute
   limit of 4*TIMEOUT before we wipe things (for random sockets).
   If force is set, always return a result, even if we have
   to allocate above the limit, and don'y free any records.
   This is set when allocating for DNSSEC to avoid cutting off
   the branch we are sitting on. */
static struct frec *get_new_frec(time_t now, struct server *master, int force)
{
  struct frec *f, *oldest, *target;
  int count;
  
  /* look for free records, garbage collect old records and count number in use by our server-group. */
  for (f = daemon->frec_list, oldest = NULL, target =  NULL, count = 0; f; f = f->next)
    {
      if (!f->sentto)
	target = f;
      else
	{
#ifdef HAVE_DNSSEC
	  /* Don't free DNSSEC sub-queries here, as we may end up with
	     dangling references to them. They'll go when their "real" query 
	     is freed. */
	  if (!f->dependent && !force)
#endif
	    {
	      if (difftime(now, f->time) >= 4*TIMEOUT)
		{
		  free_frec(f);
		  target = f;
		}
	      else if (!oldest || difftime(f->time, oldest->time) <= 0)
		oldest = f;
	    }
	}
      
      if (f->sentto && ((int)difftime(now, f->time)) < TIMEOUT && server_samegroup(f->sentto, master))
	count++;
    }

  if (!force && count >= daemon->ftabsize)
    {
      query_full(now, master->domain);
      return NULL;
    }
  
  if (!target && oldest && ((int)difftime(now, oldest->time)) >= TIMEOUT)
    { 
      /* can't find empty one, use oldest if there is one and it's older than timeout */
      free_frec(oldest);
      target = oldest;
    }
  
  if (!target && (target = (struct frec *)whine_malloc(sizeof(struct frec))))
    {
      target->next = daemon->frec_list;
      daemon->frec_list = target;
    }

  if (target)
    target->time = now;

  return target;
}

static void query_full(time_t now, char *domain)
{
  static time_t last_log = 0;
  
  if ((int)difftime(now, last_log) > 5)
    {
      last_log = now;
      if (!domain || strlen(domain) == 0)
	my_syslog(LOG_WARNING, _("Maximum number of concurrent DNS queries reached (max: %d)"), daemon->ftabsize);
      else
	my_syslog(LOG_WARNING, _("Maximum number of concurrent DNS queries to %s reached (max: %d)"), domain, daemon->ftabsize);
    }
}


static struct frec *lookup_frec(unsigned short id, int fd, void *hash, int *firstp, int *lastp)
{
  struct frec *f;
  struct server *s;
  int first, last;
  struct randfd_list *fdl;
  
  for(f = daemon->frec_list; f; f = f->next)
    if (f->sentto && f->new_id == id && 
	(memcmp(hash, f->hash, HASH_SIZE) == 0))
      {
	filter_servers(f->sentto->arrayposn, F_SERVER, firstp, lastp);

	/* sent from random port */
	for (fdl = f->rfds; fdl; fdl = fdl->next)
	  if (fdl->rfd->fd == fd)
	  return f;
	
	/* Sent to upstream from socket associated with a server. 
	   Note we have to iterate over all the possible servers, since they may
	   have different bound sockets. */
	for (first = *firstp, last = *lastp; first != last; first++)
	  {
	    s = daemon->serverarray[first];
	    if (s->sfd && s->sfd->fd == fd)
	      return f;
	  }
      }
  
  return NULL;
}

static struct frec *lookup_frec_by_query(void *hash, unsigned int flags, unsigned int flagmask)
{
  struct frec *f;

  for(f = daemon->frec_list; f; f = f->next)
    if (f->sentto &&
	(f->flags & flagmask) == flags &&
	memcmp(hash, f->hash, HASH_SIZE) == 0)
      return f;
  
  return NULL;
}

/* Send query packet again, if we can. */
void resend_query()
{
  if (daemon->srv_save)
    server_send(daemon->srv_save, daemon->fd_save,
		daemon->packet, daemon->packet_len, 0);
}

/* A server record is going away, remove references to it */
void server_gone(struct server *server)
{
  struct frec *f;
  int i;
  
  for (f = daemon->frec_list; f; f = f->next)
    if (f->sentto && f->sentto == server)
      free_frec(f);

  /* If any random socket refers to this server, NULL the reference.
     No more references to the socket will be created in the future. */
  for (i = 0; i < daemon->numrrand; i++)
    if (daemon->randomsocks[i].refcount != 0 && daemon->randomsocks[i].serv == server)
      daemon->randomsocks[i].serv = NULL;
  
  if (daemon->srv_save == server)
    daemon->srv_save = NULL;
}

/* return unique random ids. */
static unsigned short get_id(void)
{
  unsigned short ret = 0;
  struct frec *f;
  
  while (1)
    {
      ret = rand16();

      /* ensure id is unique. */
      for (f = daemon->frec_list; f; f = f->next)
	if (f->sentto && f->new_id == ret)
	  break;

      if (!f)
	return ret;
    }
}

#ifdef ZXIC_ONELINK_TEST
/**
 * 清除已认证MAC地址的iptables限制规则，允许正常上网
 * @param mac MAC地址字符串
 */
static void cleanup_authenticated_mac_rules(const char *mac)
{
	char cmd[256];
	int max_attempts = 10; // 最多尝试10次，避免无限循环
	int i;

	if (!mac || strlen(mac) == 0) {
		printf("corem dnsmasq cleanup_authenticated_mac_rules: invalid MAC address\n");
		return;
	}

	printf("corem dnsmasq cleanup_authenticated_mac_rules: removing restrictions for authenticated MAC %s\n", mac);

	// 删除IPv4 iptables中该MAC的DNS允许规则（已认证用户不需要特殊DNS规则）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D FORWARD -m mac --mac-source %s -p udp --dport 53 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break; // 删除失败，说明规则不存在，退出循环
		}
	}

	// 删除IPv4 iptables中该MAC的HTTP允许规则（INPUT链，已认证用户不需要特殊HTTP规则）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D INPUT -m mac --mac-source %s -p tcp --dport 80 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	// 删除IPv4 iptables中该MAC的HTTPS允许规则（INPUT链，已认证用户不需要特殊HTTPS规则）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D INPUT -m mac --mac-source %s -p tcp --dport 443 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	// 删除IPv4 iptables中该MAC的DROP规则（最重要：移除阻止规则）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D FORWARD -m mac --mac-source %s -j DROP 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break; // 没有找到规则，退出循环
		}
	}

	// 删除IPv6 ip6tables中该MAC的DROP规则
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"ip6tables -D FORWARD -m mac --mac-source %s -j DROP 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	printf("corem dnsmasq cleanup_authenticated_mac_rules: completed cleanup for authenticated MAC %s\n", mac);
}
#endif
