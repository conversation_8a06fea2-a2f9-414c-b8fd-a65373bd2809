#!/bin/bash

# IPv6 Captive Portal 编译测试脚本

echo "=== IPv6 Captive Portal 编译测试 ==="

# 设置编译环境
export CC=gcc
export CFLAGS="-Wall -Wextra -std=c99"

# 测试1: 启用IPv6支持编译
echo "测试1: 启用IPv6支持编译..."
$CC $CFLAGS -DCONFIG_IPV6_PORTAL -DJCV_FEATURE_CAPTIVE_PORTAL_SERVER -DQRZL_ONE_LINK_AUTH -DQRZL_CMP_AUTH -DJCV_FEATURE_INTER_AD_PAGE_AUTH -c qrzl_captive_portal_server.c -o test_ipv6.o 2>&1

if [ $? -eq 0 ]; then
    echo "✅ IPv6支持编译成功"
    rm -f test_ipv6.o
else
    echo "❌ IPv6支持编译失败"
fi

# 测试2: 禁用IPv6支持编译
echo "测试2: 禁用IPv6支持编译..."
$CC $CFLAGS -DJCV_FEATURE_CAPTIVE_PORTAL_SERVER -DQRZL_ONE_LINK_AUTH -DQRZL_CMP_AUTH -DJCV_FEATURE_INTER_AD_PAGE_AUTH -c qrzl_captive_portal_server.c -o test_ipv4.o 2>&1

if [ $? -eq 0 ]; then
    echo "✅ IPv4支持编译成功"
    rm -f test_ipv4.o
else
    echo "❌ IPv4支持编译失败"
fi

# 测试3: 最小配置编译
echo "测试3: 最小配置编译..."
$CC $CFLAGS -DJCV_FEATURE_CAPTIVE_PORTAL_SERVER -c qrzl_captive_portal_server.c -o test_minimal.o 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 最小配置编译成功"
    rm -f test_minimal.o
else
    echo "❌ 最小配置编译失败"
fi

echo "=== 编译测试完成 ==="

# 显示宏定义说明
echo ""
echo "=== 宏定义说明 ==="
echo "CONFIG_IPV6_PORTAL     - 启用IPv6支持"
echo "QRZL_ONE_LINK_AUTH     - 启用移动认证"
echo "QRZL_CMP_AUTH          - 启用电信认证"
echo "JCV_FEATURE_INTER_AD_PAGE_AUTH - 启用广告页面认证"
echo "JCV_FEATURE_CAPTIVE_PORTAL_SERVER - 启用captive portal服务器"
