/*
 *  wlan-server.c -- Wlan Deal module
 *
 *  Copyright (c) ZTE Corporation All Rights Reserved.
 *
 *  $Id: wlan-server.c,v 0.1 2010-12-15 $
 *  Authors :   ZHAOYONG - <EMAIL>>
 */
/****************ͷ�ļ�*/
#include <signal.h>
#include <time.h>
#include "wlan_main.h"
#include "wifi_util.h"
#include <soft_timer.h>

#include "wlan_config_ssid.h"
#include "rtk_arch.h"
#include <semaphore.h>

#include "wifi_sta_ctrl.h"
#include "wlan_sta_manager.h"


#include "wlan_rtc_sleep.h"
#ifdef __AP_FUNC__
#include "wifi_ap_ctrl.h"
#endif
#include "hotplug.h"

/**************************�궨��*************************/

//#define UEVENT_BUFFER_SIZE   1024*2
#define  CHARGER     "/sys/class/power_supply/charger/status"
#define  WIFI_DIR				"/etc_rw/wifi"

#if (defined(USE_CAP_SUPPORT) || defined(WIFI_UNCOEXIST_5G))
int g_work_mode = IN_WIFI_WORK_MODE_AP0; //default
#endif


/*************************��������**************************************/
static int g_wifi_testmode = 0;

static int g_pre_is_charging = 0;

struct   wlan_ap_server  * ap_server = NULL;
struct   wlan_sta_manager  *sta_manager= NULL;

#ifdef __STA_FUNC__
sem_t  g_wpa_supplicant_id;
extern struct  wlan_sta_manager   esp8089_sta;
extern struct  wlan_sta_manager   rda5995_sta;
#ifdef __REALTEK_8192_CHIP__
extern struct  wlan_sta_manager   realtek_sta;
#endif
extern struct  wlan_sta_manager   ssv6x5x_sta;
extern struct  wlan_sta_manager   aic8800dw_sta;

#endif

extern void *netlink_loop (void *param);
extern int wifi_app_msg_parse(const char *msg, int msglen, struct hotplug_event *event);
void check_wifi_mac();
#ifdef ZXIC_ONELINK_TEST
static void cleanup_mac_iptables_rules(const char *mac);
#endif

#ifdef __AP_FUNC__
extern struct wlan_ap_server  esp8089_ap;
extern struct wlan_ap_server  rda5995_ap;
extern struct wlan_ap_server  xr819_ap;
extern struct wlan_ap_server  ssv6x5x_ap;
extern struct wlan_ap_server  aic8800dw_ap;


#ifdef __REALTEK_8192_CHIP__
extern struct wlan_ap_server  realtek_8192_ap;
extern int wps_rtc_timer_open ;
extern void wlan_get_wps_access_sta();
extern void handle_wps_result (MSG_BUF *pstMsg);
extern void handle_wps_mac (MSG_BUF *pstMsg);
#endif

extern int g_rtc_wake_timer_open;
extern int g_sleep_rtc_timer;

extern int g_tsw_sleep_flag ;//���޴�����ʱ˯�ߵ�rtc timer
extern int g_tsw_wake_flag ;//���޴�����ʱ���ѵ�rtc timer
extern sem_t g_hostap_id;


static char tem_cfg[512] = {0};
#define CFG2WEB_UNCONDITIONAL(SrcCfg,DesCfg)\
                    do{     \
                        memset(tem_cfg,0,sizeof(tem_cfg));                   \
                        cfg_get_item(SrcCfg,tem_cfg,sizeof(tem_cfg));       \
                        cfg_set(DesCfg,tem_cfg);                             \
                    }while(0)
extern void wifi_station_close (void);

/*************************��̬��������***********************************/
static int chargingcheckstates();
static void sleep_wifi();
void basic_deal_all (int cmd);
extern  void zte_get_wifi_sta_list(RT_802_11_MAC_TABLE *staList);


//��ʱ����: ��Ϊ��TswWakeLoop �յ�elapse��Ϣ�����������timer�ˣ��˴�
//�����������ã�ֻ��ִ�л��Ѳ���
static void handle_wifi_tsw_open()
{
	char wifi_cur_state[8] = {0};
	char m_ssid_enable[8] = {0};
	cfg_get_item ("wifi_cur_state", wifi_cur_state, sizeof (wifi_cur_state));
	cfg_get_item ("m_ssid_enable", m_ssid_enable, sizeof (m_ssid_enable));

	wf_log ("handle_wifi_tsw_open");

	if (strcmp (wifi_cur_state, WIFI_OPENED) == 0) {
		wf_log ("wifi is already open\n ");
		return;
	}

	if (1 == atoi (m_ssid_enable)) {
		basic_deal_all (WIFI_CFG_OPEN_VA0_VA1);
	} else {
		basic_deal_all (WIFI_CFG_OPEN);
	}
}

int is_charging()
{
	int status = 0;
	char wan_mode[16] = {0};

	cfg_get_item ("WAN_MODE", wan_mode, sizeof (wan_mode));

	if (chargingcheckstates() == 1 // is charging , no need to sleep
	    || !strcmp (wan_mode, "USB")) { //  if usb is linked , no need to sleep , no concern charging ro not

		wf_log ("******IS CHARING*******");

		status = 1;

	} else {

		status = 0;
	}

	return status;
}



static void qrcode_make()
{
	if(0 == ap_server->ap0_state)
		return;

#ifdef __QRCODE_WIFI__
	write_status (QRSTATUS, "0");
	system (QRCODE_SCRIPT_DIR"/zte_qrcode_create.sh wifi_create");
#ifndef __SINGLE_AP__
	system (QRCODE_SCRIPT_DIR"/zte_qrcode_create.sh multi_wifi_create");
#endif
	write_status (QRSTATUS, "1");
	wf_log ("zte_qrcode_create.sh wifi_create");
#endif
}







void basic_deal_all (int cmd)
{
	//remove_all_acl_timer();
	cancel_all_timer();

	ap_server->basic_deal (ap_server, cmd);
	qrcode_make();

	wlan_prepare_sleep();
}

//���úڰ�����: 0����������1����������2����������
void acl_mode_set()
{
	char ACL_mode[8] = {0};

	cfg_get_item ("ACL_mode", ACL_mode, sizeof (ACL_mode));
	CFG2WEB_UNCONDITIONAL ("ACL_mode", "AccessPolicy0");

	if (!strcmp (ACL_mode, "1")) {       // white-list
		CFG2WEB_UNCONDITIONAL ("wifi_mac_white_list", "AccessControlList0");
	} else if (!strcmp (ACL_mode, "2")) { // black-list
		CFG2WEB_UNCONDITIONAL ("wifi_mac_black_list", "AccessControlList0");
	} else {
		cfg_set ("AccessPolicy0", "0");
	}

	
}

void acl_mode_set_5g()
{
	char ACL_mode[8] = {0};

	cfg_get_item ("ACL_mode_5g", ACL_mode, sizeof (ACL_mode));
	CFG2WEB_UNCONDITIONAL ("ACL_mode_5g", "AccessPolicy0");

	if (!strcmp (ACL_mode, "1")) {       // white-list
		CFG2WEB_UNCONDITIONAL ("wifi_mac_white_list_5g", "AccessControlList0");
	} else if (!strcmp (ACL_mode, "2")) { // black-list
		CFG2WEB_UNCONDITIONAL ("wifi_mac_black_list_5g", "AccessControlList0");
	} else {
		cfg_set ("AccessPolicy0", "0");
	}
}

#if (defined(USE_CAP_SUPPORT) || defined(WIFI_UNCOEXIST_5G))
/* Started by AICoder, pid:kb51fx16f0x34a8147ec0905f0f5ad145ee8f9db */
static void set_mac_access()
{
	wf_log ("nxl  set_mac_access  ");
	if (g_work_mode == IN_WIFI_WORK_MODE_AP1) {
		acl_mode_set_5g();
	}
	else {
		acl_mode_set();
	}
	

	if (ap_server->ap0_state == 1) {
		wf_log ("wifi is on");
		acl_set_process();   
	} else {
		wf_log ("wifi is off");
	}
}
/* Ended by AICoder, pid:kb51fx16f0x34a8147ec0905f0f5ad145ee8f9db */
#else
static void set_mac_access()
{
	wf_log ("nxl  set_mac_access  ");
	acl_mode_set();

	if (ap_server->ap0_state == 1) {
		wf_log ("wifi is on");
		acl_set_process();   
	} else {
		wf_log ("wifi is off");
	}
}
#endif


static int chargingcheckstates()
{
	char buf_status[16] = {0};
	int ret = 0;
	int  errno_1 = 0;//errno value for the status file of prefer supply

	FILE *p_status = NULL;
	int len = 0;

	p_status = fopen (CHARGER, "r");
	if (p_status == NULL) {
		wf_log ("errno_1 = %d, charger is not exist", errno_1);
		return 1;
	}
	
	len = fread(buf_status, 1, sizeof(buf_status), p_status);//cov m
	if (len > 0) {
		if ( (strncmp (buf_status, "Charging", 8) == 0) || (strncmp (buf_status, "Full", 4) == 0)) { //buf�����һλ��ӡ�����ǻ�����strcmp�Ƚ���Զ���ɹ������Ը���strncmp
			ret = 1;
		} else {
			ret = 0;
		}
	}
	
	if (fclose(p_status)) { //kw
		wf_log("fclose error");
	}
	return ret;
}

//ÿ�ο���������ô˺�����ͨ��˯��ʱ�䳤���жϣ��������Ƿ�����˯�ߣ�����rtc������ʱ

static void sleep_wifi()
{
	cfg_set ("wifiSleep", "1");
	cancel_all_timer();
	ap_server->basic_deal (ap_server, WIFI_CFG_SLEEP); //sleep no real action meaning,just readable
	
	wf_msg_to_zcore(MSG_CMD_WIFI_SET_OFF_RSP, 2, WIFI_CLOSED);	//wifi�رպ�֪ͨ�̶�̨��ʾwifi״̬
}

// if  wifi is off ,  charging  in wps, no need to sleep
// only sta_count ==0 ,can sleep
static void handle_sleep_wifi()
{

	if (ap_server->ap0_state == 0  || g_pre_is_charging == 1  || ap_server->ap0_wps_state  == 1)
		return;

	if (captureWlanStaInfo() == 0) {
		sleep_wifi();
	}
}

//��ʱ˯�ߴ�������:������û�� sta �����豸������û�п���wps��Ҫ�ر� wifi
static void handle_wifi_tsw_close()
{
	if (ap_server->ap0_state == 0)
		return;
	sleep_wifi();
}



void process_rtc_elapsed (MSG_BUF *pstMsg)
{
	UINT32  module_id = 0;
	RTC_DATA_BUF *data_buf = pstMsg->aucDataBuf;
	module_id = data_buf->module;

	switch (module_id) {
	case WIFI_INTERVAL_SLEEP: {
		g_sleep_rtc_timer = 0; // no need to delete RTCTimer
		handle_sleep_wifi();
		break;
	}
	case WIFI_TSW_SLEEP: {
		g_tsw_sleep_flag = 0;
		//�˴����Ӻ�����������˯��ʱ��Ϊ24Сʱ
		create_rtc_tsw_close_timer (24 * 60 * 60);
		handle_wifi_tsw_close();
		break;
	}
	case WIFI_TSW_WAKEUP: {
		g_tsw_wake_flag = 0;
		//�˴����Ӻ�����������˯��ʱ��Ϊ24Сʱ
		create_rtc_tsw_open_timer (24 * 60 * 60);
		handle_wifi_tsw_open();
		break;
	}
	case WIFI_WAKE_UP_SYSTEM: {
		g_rtc_wake_timer_open = 0;
#ifdef __REALTEK_8192_CHIP__
		captureWlanStaInfo();
#endif
		wlan_prepare_sleep();
		break;
	}

#ifdef __REALTEK_8192_CHIP__	
	case WIFI_WPS_RESULT_CHECK: {
		wps_rtc_timer_open = 0;// timer close automaticly when alarm elapsed
		wlan_get_wps_access_sta();
		break;
	}
	case WIFI_ACL_SERVICE: {
		break;
	}
#endif
	default:
		break;
	}
}

void usb_charging_changed()
{
	int charging_status = 0;
	charging_status = chargingcheckstates();
	char fake_poweroff[8]={0};
	char wifiSleep[8]={0};

	cfg_get_item ("mmi_fake_poweroff_state", fake_poweroff, sizeof (fake_poweroff));
	cfg_get_item ("wifiSleep", wifiSleep, sizeof (wifiSleep));
	if (charging_status != g_pre_is_charging) { // ��֮ǰ��һ�δ���usb״̬��ͬ
		g_pre_is_charging = charging_status;
		wf_log ("charging_status =%d,ap_server->ap0_state=%d,going into handle_usb_msg", g_pre_is_charging, ap_server->ap0_state);
		if (ap_server->ap0_state == 1) {
			//wifi is open
			wlan_prepare_sleep();
		}
		// wifi ˯�ߺ󣬲���usb���ɻ���, ���Ǽٹػ�������
		else if (ap_server->ap0_state == 0 && g_pre_is_charging == 1 
				&& strcmp (fake_poweroff, "1") != 0 // �Ҳ��Ǽٹػ�
				&& strcmp (wifiSleep, "1") == 0) {// ����˯�����������wifi�رղſ��Ի���
			char m_ssid_enable[8] = {0};
			cfg_get_item ("m_ssid_enable", m_ssid_enable, sizeof (m_ssid_enable));
			if (1 == atoi (m_ssid_enable)) {
				basic_deal_all (WIFI_CFG_OPEN_VA0_VA1);
			} else {
				basic_deal_all (WIFI_CFG_OPEN);
			}
		}
	}
}


void fwp_charging_changed(int charging_status)
{
	char wifiSleep[8]={0};
	cfg_get_item ("wifiSleep", wifiSleep, sizeof (wifiSleep));

	g_pre_is_charging = charging_status;
	wf_log ("charging_status =%d,ap_server->ap0_state=%d", charging_status, ap_server->ap0_state);
	if (ap_server->ap0_state == 1) {
		wlan_prepare_sleep();
	}
	else if (ap_server->ap0_state == 0 && charging_status == 1 
		&& strcmp (wifiSleep, "1") == 0) {// ����˯�����������wifi�رղſ��Ի���
		basic_deal_all (WIFI_CFG_OPEN);
		// for R1680 notify cp mmi wifi is on
		wf_msg_to_zcore(MSG_CMD_WIFI_SET_ON_RSP, 2, WIFI_OPENED);	//wifi�رպ�֪ͨ�̶�̨��ʾwifi״̬
	}
	else{
		//do nothing
	}
}


void wifi_wps_set (MSG_BUF *pstMsg)
{
	char encrypType[12]    = {0};
	char wifi_wps_index[8] = {0};
	char hidessid[20] = {0};
	char authmode[20] = {0};
	char wifi_cur_state[8] = {0};

	cfg_get_item ("wifi_cur_state", wifi_cur_state, sizeof (wifi_cur_state));
	if (pstMsg->src_id == MODULE_ID_MMI) {
		strcpy (wifi_wps_index, "1");
		cfg_set ("wifi_wps_index", "1");
	} else {
		cfg_get_item ("wifi_wps_index", wifi_wps_index, sizeof (wifi_wps_index));
	}

	if (strcmp (wifi_wps_index, "1") == 0) {
		cfg_get_item ("AuthMode", authmode, sizeof (authmode));
		cfg_get_item ("HideSSID", hidessid, sizeof (hidessid));
		cfg_get_item ("EncrypType", encrypType, sizeof (encrypType));
	} else if (strcmp (wifi_wps_index, "2") == 0) {
		cfg_get_item ("m_AuthMode", authmode, sizeof (authmode));
		cfg_get_item ("m_HideSSID", hidessid, sizeof (hidessid));
		cfg_get_item ("m_EncrypType", encrypType, sizeof (encrypType));
	}

	//if((!strncmp(RadioOff, "1", 2)) && (!strncmp(hidessid, "0", 2)))

	if (!strncmp (wifi_cur_state, WIFI_OPENED, 2) && !strncmp (hidessid, "0", 1)
	    && strcmp (encrypType, "TKIP") && strcmp (authmode, "WPAPSK") && strcmp (authmode, "WAPIPSK")
	    && (strcmp (authmode, "SHARED") && strcmp (authmode, "WEPAUTO"))
	    && ! (!strcmp (authmode, "OPEN") && !strcmp (encrypType, "WEP"))) {
		wps_deal (pstMsg);
	} else {
		cfg_set ("WscModeOption", "0");
		wf_log ("Invalid WPS para!");
	}
}

void wifi_advanced_set (MSG_BUF *pstMsg)
{
	UCHAR flags[8] = {0};
	if (pstMsg->usDataLen > 0) {
		wf_log ("aucDataBuf=%s, usDataLen=%d", pstMsg->aucDataBuf, pstMsg->usDataLen);
		memcpy (flags, pstMsg->aucDataBuf, pstMsg->usDataLen);
		wf_log ("flags=%s", flags);
		switch (atoi (flags)) {
		case WIFI_ADVANCED_CLOSE:
			//RadioOff should be set to 0 in webserver, to be fix
		{
			if(ap_server->ap0_state)basic_deal_all (WIFI_CFG_CLOSE);
			//handle_tsw_close();//����Ҫɾ����ʱ˯�߻��Ѷ�ʱ��
			break;
		}
		case WIFI_ADVANCED_OPEN: {
			wf_log ("ap_server->ap0_state=%d,ap_server->ap1_state=%d", ap_server->ap0_state, ap_server->ap1_state);
			if (ap_server->ap0_state && ap_server->ap1_state) {
				wf_log ("va1 is on,so close va1");
				basic_deal_all (WIFI_CFG_CLOSE_MSSID);
			} else {
				if (!ap_server->ap0_state)basic_deal_all (WIFI_CFG_OPEN);
			}
			break;
		}
		//  RadioOff=0  wifiEnabled =0  to close wifi
		//  wifiEnabled=1  to open wifi
		// no para data to reset wifipara , need restart wifi
		case WIFI_ADVANCED_CLOSE_MSSID: {
			basic_deal_all (WIFI_CFG_CLOSE_MSSID);
			break;
		}
		case WIFI_ADVANCED_OPEN_VA01: {
			cfg_set ("wifi_sta_connection", "0"); //���Ᵽ��
			basic_deal_all (WIFI_CFG_OPEN_VA0_VA1);
			break;
		}
		case WIFI_ADVANCED_OPEN_VA1: {
			cfg_set ("wifi_sta_connection", "0"); //���Ᵽ��
			basic_deal_all (WIFI_CFG_OPEN_MSSID);
			break;
		}
		case WIFI_ADVANCED_RF: {
			basic_deal_all (WIFI_CFG_RF); // RF nv value already set OK,just restart
			break;
		}
		case WIFI_ADVANCED_RESTART: { //from tr069,  set ssid password, channel,  wifi_coverage etc..
			basic_deal_all (WIFI_CFG_FORCE_RESTART); // just  forcerestart
			break;
		}
		case WIFI_ADVANCED_BAND_SWITCH: { //ap 2.4G/5G �л�
			basic_deal_all (WIFI_CFG_BAND_SWITCH); 
			break;
		}

		}
		//free(pData);
	}
}
static void wifi_testmode_set (MSG_BUF *pstMsg)
{
	int flag = 0;
	int ret = 0;
	
#if defined(__AIC_8800DW_CHIP__)	
	if (pstMsg->usDataLen > 0) {
		flag = atoi(pstMsg->aucDataBuf);
		wf_log ("flag=[%s,%d,%d]", pstMsg->aucDataBuf, flag, g_wifi_testmode);
		
		if (flag == 1) {//MSG_CMD_WIFI_CLOSE
			wf_log("entry wifitest");
			if (g_wifi_testmode != 1) {
				if ( ap_server->ap0_state) {
					basic_deal_all (WIFI_CFG_CLOSE);
				}
				wf_msg_to_zcore(MSG_CMD_WIFI_SET_OFF_RSP, 0, NULL);

				ret = wlan_drv_test_init();
				if (ret != 0) {
					wf_log("wlan_drv_test_init fail");
					ipc_send_message(MODULE_ID_WIFI, pstMsg->src_id, MSG_CMD_WIFI_TESTMODE_RSP, 2, "1", 0);
					return;
				}
				g_wifi_testmode = 1;
			}
			ipc_send_message(MODULE_ID_WIFI, pstMsg->src_id, MSG_CMD_WIFI_TESTMODE_RSP, 2, "0", 0);
			return;
		}
		else if (flag == 0) {//MSG_CMD_WIFI_OPEN
			wf_log("exit wifitest");
			if (g_wifi_testmode == 1) {
				ret = wlan_drv_test_deinit();
				if (ret != 0) {
					wf_log("wlan_drv_test_deinit fail");
					ipc_send_message(MODULE_ID_WIFI, pstMsg->src_id, MSG_CMD_WIFI_TESTMODE_RSP, 2, "1", 0);
					return;
				}
				g_wifi_testmode = 0;
			
				if (! ap_server->ap0_state) {
					char m_ssid_enable[8] = {0};
					cfg_get_item ("m_ssid_enable", m_ssid_enable, sizeof (m_ssid_enable));
					if (1 == atoi (m_ssid_enable)) {
						basic_deal_all (WIFI_CFG_OPEN_VA0_VA1);
					} else {
						basic_deal_all (WIFI_CFG_OPEN);
					}
				}
				wf_msg_to_zcore(MSG_CMD_WIFI_SET_ON_RSP, 0, NULL);
			}
			ipc_send_message(MODULE_ID_WIFI, pstMsg->src_id, MSG_CMD_WIFI_TESTMODE_RSP, 2, "0", 0);
			return;
		}
	}
#endif
	wf_log ("unsupport wifitest cmd!");

	ipc_send_message(MODULE_ID_WIFI, pstMsg->src_id, MSG_CMD_WIFI_TESTMODE_RSP, 2, "1", 0);
	
}

void deal_msg_data(MSG_BUF *pMsg)
{
	switch (pMsg->usMsgCmd) {
		case MSG_CMD_WIFI_CFG_AP:
		case MSG_CMD_WIFI_CFG_SSID_REQ:
		case MSG_CMD_WIFI_CFG_PWD_REQ:
		case MSG_CMD_WIFI_SET_SECURITY_REQ:
			if(pMsg->usDataLen != 0){
				wlan_basic_info *info = (wlan_basic_info*)(pMsg->aucDataBuf);

				wf_log("ssid=%s ,pwd=%s, auth=%s, encrypt=%s \n",info->ssid, info->pwd, info->authmode,info->encrypt);

				if(strlen(info->ssid)>0)
					cfg_set("SSID1",info->ssid);
				if(strlen(info->pwd)>0) {
					char *encode = NULL;
					cfg_set("WPAPSK1",info->pwd);
					encode = wlan_base64_encode(info->pwd, strlen (info->pwd));
					//kw 3
					cfg_set ("WPAPSK1_encode", encode);
					free(encode);
				}
				if(strlen(info->authmode)>0)
					cfg_set("AuthMode",info->authmode);
				if(strlen(info->encrypt)>0)
					cfg_set("EncrypType",info->encrypt);
				if(strlen(info->max_access_num)>0)
					cfg_set("MAX_Access_num",info->max_access_num);
				if(strlen(info->hidessid)>0)
					cfg_set("HideSSID",info->hidessid);				
			}
			break;
		case MSG_CMD_WIFI_CFG_AP2:
			if(pMsg->usDataLen != 0){
				wlan_basic_info *info = (wlan_basic_info*)(pMsg->aucDataBuf);
				if(strlen(info->ssid)>0)
					cfg_set("m_SSID",info->ssid);
				if(strlen(info->pwd)>0)
					cfg_set("m_WPAPSK1",info->pwd);
				if(strlen(info->authmode)>0)
					cfg_set("m_AuthMode",info->authmode);
				if(strlen(info->encrypt)>0)
					cfg_set("m_EncrypType",info->encrypt);
				if(strlen(info->max_access_num)>0)
					cfg_set("m_MAX_Access_num",info->max_access_num);
				if(strlen(info->hidessid)>0)
					cfg_set("m_HideSSID",info->hidessid);				
			}
			break;
		default:
			break;
	}
}

void  wlan_switch_reply(MSG_BUF *pMsg)
{
	int expect_status =  atoi(pMsg->aucDataBuf);

	if((WLAN_OFF == expect_status  && WLAN_OFF  == ap_server->ap0_state) ||
	   (WLAN_ON == expect_status  && WLAN_ON  == ap_server->ap0_state)){
		ipc_send_message(MODULE_ID_WIFI, pMsg->src_id, MSG_CMD_WIFI_SWITCH_RSP, 2, "0", 0);
	}else{
		ipc_send_message(MODULE_ID_WIFI, pMsg->src_id, MSG_CMD_WIFI_SWITCH_RSP, 2, "1", 0);
	}

}
wlan_mac_info_list mac_list ;
void dump_mac_list()
{
	int i=0;
	char station_mac_tmp[MAX_NUMBER_OF_MAC*20] = {0};


	for(i=0;i<mac_list.access_count;i++){
		if(i!=0)strcat(station_mac_tmp,";");
		strcat(station_mac_tmp, mac_list.mac_info[i].mac);
		wf_log("mac[%d]= %s",i, mac_list.mac_info[i].mac);
	}

	cfg_set("station_mac", station_mac_tmp);
	wf_log("station_mac= %s",station_mac_tmp);
}

void update_sta_count(int num)
{

	char sta_count[12] = {0}; //klocwork
	sprintf(sta_count, "%d", num);
	cfg_set ("sta_count", sta_count);
	ap_server->g_sta_num = num;

}
void add_sta_mac(unsigned char *mac)
{
	if (mac_list.access_count >= MAX_NUMBER_OF_MAC-1) {
		wf_log("mac_list over limit = %d!!!",mac_list.access_count);
		return;
	}
	strncpy(mac_list.mac_info[mac_list.access_count++].mac, mac, sizeof(mac_list.mac_info[0].mac)-1);//klocwork ע����++
	
	dump_mac_list();
	update_sta_count(mac_list.access_count);

#ifdef ZXIC_ONELINK_TEST

	char cmd[256] = {0};
	char one_link_authed_mac[1024] = {0};

	wf_log("salvikie add_sta_mac [%s]\n", mac);

	// 获取已认证的 MAC 地址  格式为 7a:8e:a2:03:11:85;fe:14:df:38:2b:bd; (qrzl_app 调用运营商api 获取)
	cfg_get_item("one_link_authed_mac", one_link_authed_mac, sizeof(one_link_authed_mac));
	wf_log("salvikie one_link_authed_mac [%s]\n", one_link_authed_mac);

	if (strlen(one_link_authed_mac) > 0) {
		// 构造搜索模式 "mac;"
		char search_pattern[40] = {0};
		snprintf(search_pattern, sizeof(search_pattern), "%s;", mac);

		if (strstr(one_link_authed_mac, search_pattern) != NULL) {
			wf_log("mac %s 已认证 直接放行\n", mac);
			return;
		}
	}

	// 对于未认证的MAC，添加限制规则
	wf_log("mac %s 未认证，添加限制规则\n", mac);

	// 添加IPV4限制规则后 不会弹出认证页面
	// // 允许 DNS 解析（若需通过域名访问）
	// snprintf(cmd, sizeof(cmd),
	// 	"iptables -C FORWARD -m mac --mac-source %s -p udp --dport 53 -j ACCEPT 2>/dev/null || "
	// 	"iptables -I FORWARD -m mac --mac-source %s -p udp --dport 53 -j ACCEPT",
	// 	mac, mac);
	// system(cmd);

	// // 允许访问本地web界面（HTTP 80端口到本机）
	// snprintf(cmd, sizeof(cmd),
	// 	"iptables -C INPUT -m mac --mac-source %s -p tcp --dport 80 -j ACCEPT 2>/dev/null || "
	// 	"iptables -I INPUT -m mac --mac-source %s -p tcp --dport 80 -j ACCEPT",
	// 	mac, mac);
	// system(cmd);

	// // 允许访问本地web界面（HTTPS 443端口到本机）
	// snprintf(cmd, sizeof(cmd),
	// 	"iptables -C INPUT -m mac --mac-source %s -p tcp --dport 443 -j ACCEPT 2>/dev/null || "
	// 	"iptables -I INPUT -m mac --mac-source %s -p tcp --dport 443 -j ACCEPT",
	// 	mac, mac);
	// system(cmd);

	// // 阻止该 MAC 地址访问外网的所有流量（FORWARD链）
	// snprintf(cmd, sizeof(cmd),
	// 	"iptables -C FORWARD -m mac --mac-source %s -j DROP 2>/dev/null || "
	// 	"iptables -A FORWARD -m mac --mac-source %s -j DROP",
	// 	mac, mac);
	// system(cmd);

	// IPv6流量也要阻止访问外网
	// 阻止该 MAC 地址IPV6 的所有流量  为了弹出广告页面 (有些手机IPV4V6 会优先走IPV6)
	// snprintf(cmd, sizeof(cmd), "ip6tables -I FORWARD -m mac --mac-source %s -j DROP", mac);
	// system(cmd);
#endif
}


void remove_sta_mac(unsigned char *mac)
{

	int i=0,j=0;
	wf_log("mac_list.access_count = %d",mac_list.access_count);

	for(i=0;i<mac_list.access_count;i++){
		if(strcmp(mac_list.mac_info[i].mac, mac)==0){
			for(j=i+1;j<mac_list.access_count;j++ ){
				strcpy(mac_list.mac_info[j-1].mac, mac_list.mac_info[j].mac);
			}
			break;
		}
	}

	mac_list.access_count--;
	dump_mac_list();
	update_sta_count(mac_list.access_count);

#ifdef ZXIC_ONELINK_TEST
	// 清理该MAC地址相关的所有iptables规则
	cleanup_mac_iptables_rules(mac);
#endif

#if defined(JCV_FEATURE_CAPTIVE_PORTAL_SERVER) && defined(JCV_FEATURE_WIFI_RECONNECT_PORTAL)
	char cmd[64] = {0};

	wf_log("salvikie remove_sta_mac %s\n", mac);
	snprintf(cmd, sizeof(cmd), "echo del %s > /proc/cjportal/auth", mac);
	//system("echo clear > /proc/cjportal/auth");
	system(cmd);
#endif
}


#if  (PRODUCT_TYPE == PRODUCT_PHONE)	
/*R1682�̶�̨����wifi�������*/
void process_get_wifi_info()
{
	char wifi_ssid[WIFI_SSID_LEN] = {0};
	char wifi_pwd[WLAN_PSK_LEN] = {0};
	char wifi_authmode[WLAN_SECURITY_LEN] = {0};
	wlan_basic_info basic_info = {0}; //cov
	
	wf_log ("++++++++++++process_get_wifi_info++++++++++++");

	cfg_get_item ("SSID1", wifi_ssid, sizeof (wifi_ssid));
	cfg_get_item ("WPAPSK1", wifi_pwd, sizeof (wifi_pwd));
	cfg_get_item("AuthMode", wifi_authmode, sizeof(wifi_authmode));
	wf_log("+++++++++++++R1682 get wifi_ssid:%s\n",wifi_ssid);
	wf_log("+++++++++++++R1682 get wifi_pwd:%s\n",wifi_pwd);
	


	strcpy(basic_info.ssid, wifi_ssid);
	strcpy(basic_info.pwd, wifi_pwd);
	strcpy(basic_info.authmode, wifi_authmode); 
	wf_msg_to_zcore(MSG_CMD_WIFI_GET_INFO_RSP, sizeof(wlan_basic_info), &basic_info);	
	
}
#endif

#ifndef __REALTEK_8192_CHIP__
static DHCPOFFERADDR_LIST_t *find_match_sta(struct list_head *dhcp_info_list,  char* p_mac)
{
	DHCPOFFERADDR_LIST_t * p_dhcp_info = NULL;
	wf_log("p_mac = %p", p_mac);

	list_for_each_entry(p_dhcp_info, dhcp_info_list, list) {
		//if (memcmp((void *)(p_dhcp_info->dhcp_info.mac), (void *)p_mac, 6)==0)

		if((p_dhcp_info->dhcp_info.mac[0] == p_mac[0]) && (p_dhcp_info->dhcp_info.mac[1] == p_mac[1]) &&
		    (p_dhcp_info->dhcp_info.mac[2] == p_mac[2]) && (p_dhcp_info->dhcp_info.mac[3] == p_mac[3]) &&
		    (p_dhcp_info->dhcp_info.mac[4] == p_mac[4]) && (p_dhcp_info->dhcp_info.mac[5] == p_mac[5]))
		{
			return p_dhcp_info;
		}
	}
	return  NULL;
}

/**
**�����̶�̨���������ڻ�ȡ���ӵ�AP�ȵ������
*/
void process_get_user_list_req()
{
	
    int i,result=0;
	
	struct list_head dhcp_info_list;
	DHCPOFFERADDR_LIST_t * p_dhcp_info   = NULL;
	INIT_LIST_HEAD(&dhcp_info_list);
	char *mac_tmp=safe_malloc (6, 0);
	if (mac_tmp == NULL) { //kw 1
		wf_log("process_get_user_list_req malloc fail");
		goto out;
	}

	result = zte_get_mac_list_from_lease(&dhcp_info_list);
	
	if(result < 0 || mac_list.access_count == 0)
	{
		wf_msg_to_zcore(MSG_CMD_WIFI_GET_USER_LIST_RSP, 0, NULL);	
		goto out;
	}	

	for (i = 0; i < mac_list.access_count; i++) {
		
		wf_log("mac %d,is[%s]",i,mac_list.mac_info[i].mac);
		sscanf(mac_list.mac_info[i].mac, "%2x:%2x:%2x:%2x:%2x:%2x",(unsigned int *)&mac_tmp[0],(unsigned int *)&mac_tmp[1],(unsigned int *)&mac_tmp[2],(unsigned int *)&mac_tmp[3],(unsigned int *)&mac_tmp[4],(unsigned int *)&mac_tmp[5]);//kw

		wf_log("mac_tmp[%2x:%2x:%2x:%2x:%2x:%2x]",mac_tmp[0],mac_tmp[1],mac_tmp[2],mac_tmp[3],mac_tmp[4],mac_tmp[5]);
		wf_log("mac_tmp = %p", mac_tmp);
		if((p_dhcp_info = find_match_sta(&dhcp_info_list,  mac_tmp)) != NULL)
		{
			strncpy(mac_list.mac_info[i].hostname,p_dhcp_info->dhcp_info.host_name,sizeof(p_dhcp_info->dhcp_info.host_name));
			wf_log("hostname %s",mac_list.mac_info[i].hostname);
		}

	}
	wf_msg_to_zcore(MSG_CMD_WIFI_GET_USER_LIST_RSP, sizeof(wlan_mac_info_list), &mac_list);


out:
	free_dhcp_list(&dhcp_info_list);
	safe_free(mac_tmp);

}

#else

static DHCPOFFERADDR_LIST_t *find_match_sta(struct list_head *dhcp_info_list,  RT_802_11_MAC_ENTRY * pEntry)
{
	DHCPOFFERADDR_LIST_t * p_dhcp_info = NULL;

	list_for_each_entry(p_dhcp_info, dhcp_info_list, list) {
		//  printf("[%s] p_dhcp_info:%d\n", __FUNCTION__, p_dhcp_info);


		if ((p_dhcp_info->dhcp_info.mac[0] == pEntry->Addr[0]) && (p_dhcp_info->dhcp_info.mac[1] == pEntry->Addr[1]) &&
		    (p_dhcp_info->dhcp_info.mac[2] == pEntry->Addr[2]) && (p_dhcp_info->dhcp_info.mac[3] == pEntry->Addr[3]) &&
		    (p_dhcp_info->dhcp_info.mac[4] == pEntry->Addr[4]) && (p_dhcp_info->dhcp_info.mac[5] == pEntry->Addr[5])) {

			return p_dhcp_info;

		}
	}


	return  NULL;

}

/**
**�����̶�̨���������ڻ�ȡ���ӵ�AP�ȵ������
*/
void process_get_user_list_req()
{
	wlan_mac_info_list mac_list ;
	
    int i,result=0;
	RT_802_11_MAC_TABLE staList             = {0};
	struct list_head dhcp_info_list;
	DHCPOFFERADDR_LIST_t * p_dhcp_info   = NULL;
	INIT_LIST_HEAD(&dhcp_info_list);

	result = zte_get_mac_list_from_lease(&dhcp_info_list);
	zte_get_wifi_sta_list(&staList);
	
	wf_log("staList.Num:%d", staList.Num);
	if(staList.Num == 0)
	{
			wf_msg_to_zcore(MSG_CMD_WIFI_GET_USER_LIST_RSP, 0, NULL);	
			goto out;
	}	
	memset(&mac_list,0,sizeof(wlan_mac_info_list));
	mac_list.access_count = staList.Num;
	for (i = 0; i < staList.Num; i++) {
			sprintf(mac_list.mac_info[i].mac, "%2.2X:%2.2X:%2.2X:%2.2X:%2.2X:%2.2X;",\
				staList.Entry[i].Addr[0], staList.Entry[i].Addr[1],
				staList.Entry[i].Addr[2], staList.Entry[i].Addr[3],
				staList.Entry[i].Addr[4], staList.Entry[i].Addr[5]);
		
		wf_log("mac %d,is:%s",i,mac_list.mac_info[i].mac);
		if((p_dhcp_info = find_match_sta(&dhcp_info_list,  &staList.Entry[i])) != NULL)
		{
			strncpy(mac_list.mac_info[i].hostname,p_dhcp_info->dhcp_info.host_name,sizeof(p_dhcp_info->dhcp_info.host_name));
			wf_log("hostname %s",mac_list.mac_info[i].hostname);
		}					

	}
	wf_msg_to_zcore(MSG_CMD_WIFI_GET_USER_LIST_RSP, sizeof(wlan_mac_info_list), &mac_list);


out:
	free_dhcp_list(&dhcp_info_list);

}
#endif



void wlan_prepare_sleep()
{
	int wifi_status = ap_server->ap0_state;
	int charging_status = g_pre_is_charging;
	int sta_num = ap_server->g_sta_num;
	int wps_status = ap_server->ap0_wps_state;

	wf_log ("wifi_status=%d, charging_status=%d, sta_num=%d, wps_status=%d", wifi_status, charging_status, sta_num, wps_status);
	check_to_presleep (wifi_status, charging_status, sta_num, wps_status);
}

static void set_globle_charging_status()
{

	g_pre_is_charging = chargingcheckstates();

	wf_log ("usb charging is %s", g_pre_is_charging ? "YES" : "NO");
}

void wlan_ap_msg_handle (MSG_BUF *pMsg)
{
	char wifi_cur_state[8] = {0};

	cfg_get_item ("wifi_cur_state", wifi_cur_state, sizeof (wifi_cur_state));

	deal_msg_data(pMsg);

	switch (pMsg->usMsgCmd) {
	case MSG_CMD_WIFI_CFG_AP:
		basic_deal_all (WIFI_CFG_AP);           //��SSID �������ã�����wlan0
		break;
		
	case MSG_CMD_WIFI_CFG_AP2:
		basic_deal_all (WIFI_CFG_MSSID);        // m_SSID ��������
		break;
		
	case MSG_CMD_WIFI_CLOSE:
	case  MSG_CMD_WIFI_SET_OFF_REQ:// fwp use
		if ( ap_server->ap0_state) {
			basic_deal_all (WIFI_CFG_CLOSE);
		}
		wf_msg_to_zcore(MSG_CMD_WIFI_SET_OFF_RSP, 0, NULL);
		break;

	case MSG_CMD_WIFI_OPEN:
    	case MSG_CMD_WIFI_SET_ON_REQ:  //fwp use
		wf_log("open wifi");
		if (! ap_server->ap0_state) {
			char m_ssid_enable[8] = {0};
			cfg_get_item ("m_ssid_enable", m_ssid_enable, sizeof (m_ssid_enable));
			if (1 == atoi (m_ssid_enable)) {
				basic_deal_all (WIFI_CFG_OPEN_VA0_VA1);
			} else {
				basic_deal_all (WIFI_CFG_OPEN);
			}
		}
		wf_msg_to_zcore(MSG_CMD_WIFI_SET_ON_RSP, 0, NULL);
		break;

// for open cpu start
	case MSG_CMD_WIFI_CFG_SSID_REQ:
		basic_deal_all (WIFI_CFG_AP);           //��SSID �������ã�����wlan0
		ipc_send_message(MODULE_ID_WIFI, pMsg->src_id, MSG_CMD_WIFI_CFG_SSID_RSP, 2, ap_server->ap0_state?"0":"1", 0);
		
		break;	
	case MSG_CMD_WIFI_CFG_PWD_REQ:
		basic_deal_all (WIFI_CFG_AP);           //��SSID �������ã�����wlan0
		ipc_send_message(MODULE_ID_WIFI, pMsg->src_id, MSG_CMD_WIFI_CFG_PWD_RSP, 2, ap_server->ap0_state?"0":"1", 0);
		
		break;		

	case MSG_CMD_WIFI_SWITCH_REQ:// open cpu  , at ctrl  use
		wifi_advanced_set (pMsg);
		wlan_switch_reply(pMsg);
		break;
// for open cpu end

	case MSG_CMD_WIFI_TESTMODE_REQ:
		wifi_testmode_set(pMsg);
		break;
		
	case MSG_CMD_WIFI_ADVANCED:
		wifi_advanced_set (pMsg);
		break;
	case MSG_CMD_WIFI_FORCE_RESTART:// broadcom ,realtek need, form netlink msg
		//�� wifi firmware crash����������̣�������webui������Ϣ���������Խ�RadioOff��0
		//cfg_set("wifi_cur_state", WIFI_CLOSED);
		//cfg_set("RadioOffException", "0");// ����Ϣ��Դͷ������ �쳣�ر�wifi���
		basic_deal_all (WIFI_CFG_FORCE_RESTART);
		break;
	case MSG_CMD_WIFI_SLEEP:
		cancel_all_timer();
		wlan_prepare_sleep();
		break;
	case MSG_CMD_WIFI_COVERAGE:
		basic_deal_all (WIFI_CFG_RF);
		break;
	case MSG_CMD_WIFI_MAC:
		//remove_all_acl_timer();
		set_mac_access();
		break;
	case MSG_CMD_WIFI_CHANNEL_FOLLOW:
		basic_deal_all (WIFI_CFG_CHANNEL_FOLLOW);
		break;
#if  (PRODUCT_TYPE == PRODUCT_PHONE)			
    /*�̶�̨��Ϣ������ʼ*/
	case MSG_CMD_WIFI_GET_STATE_REQ:
		wf_msg_to_zcore(MSG_CMD_WIFI_GET_STATE_RSP, sizeof(wifi_cur_state), wifi_cur_state);
		break;
	case MSG_CMD_WIFI_GET_INFO_REQ:
		process_get_wifi_info();
		break;
	case MSG_CMD_WIFI_GET_USER_LIST_REQ:
        	process_get_user_list_req();
	    	break;
	case MSG_CMD_WIFI_SET_SECURITY_REQ:
		basic_deal_all (WIFI_CFG_AP);           //��SSID �������ã�����wlan0
		break;
#endif		
	case MSG_CMD_AP_STA_CONNECTED:
		add_sta_mac(pMsg->aucDataBuf);
		break;
	case MSG_CMD_AP_STA_DISCONNECTED:
		remove_sta_mac(pMsg->aucDataBuf);
		//wlan_prepare_sleep();
		break;

	/*�̶�̨��Ϣ��������*/
	case MSG_CMD_WIFI_WPS:
		wifi_wps_set (pMsg);
		break;
	case MSG_CMD_WIFI_WPS_DOWN:
#ifdef __REALTEK_8192_CHIP__
		handle_wps_mac (pMsg);
		handle_wps_result (pMsg);
#endif
		wps_down (pMsg);
		wlan_prepare_sleep();
		break;
	case MSG_CMD_WIFI_WPS_UP:
		wps_up();
		wlan_prepare_sleep();
		break;
	case MSG_CMD_WIFI_TSW_SET:
		handle_tsw_setting();       //�ֱ����˯�߻���ʱ�䣬���ö�ʱ˯�߻�����ض�ʱ��
		break;
	case MSG_CMD_WIFI_TSW_CLOSE:
		handle_tsw_close();         //ɾ����ʱ˯�߻��Ѷ�ʱ��
		break;
	case MSG_CMD_RTC_TIME_UPDATE:
		handle_tsw_setting();       //rtc ʱ����º���Ҫ�����趨ʱ��
		break;
	case MSG_CMD_WIFI_RESET_TIMER:
		wlan_reset_sleep_timer();
		break;
	case MSG_CMD_USB_CHARGING_CHANGED:
		usb_charging_changed();
		break;
	case MSG_CMD_WIFI_NOTIFY_CHARGER_STATUS:
		fwp_charging_changed(*(UINT32*)pMsg->aucDataBuf);
		break;	
	case MSG_CMD_STA_COUNT_CHANGE:
#ifdef __REALTEK_8192_CHIP__
		captureWlanStaInfo();
#endif
		wf_msg_to_mmi(MSG_CMD_STA_COUNT_CHANGE, 0, NULL);
		wlan_prepare_sleep();
		break;
	case RTC_MSG_ALARM_ELAPSED:
		process_rtc_elapsed (pMsg);
		break;
	case MSG_CMD_WIFI_STATION_CONNECTED_ENABLE_AP_CMD:
		basic_deal_all (WIFI_CFG_APSTA_OPEN_AP);
		break;
	default:
		break;
	}
}


#endif
/*R1682 wifi������� end*/

//���wifi�����Ƿ���ڣ�����ʱ״̬�Ƿ�ΪLink
int check_wlan (void)
{
	int find_wlan = -1;
	int i = 0, sum = 0;

	while (-1 == find_wlan) {
		wf_log ("finding wlan  i=%d, %d s...", i , sum);
		find_wlan = wfsystem ("ifconfig wlan0");
		if (-1==find_wlan) {
			if (sum >= 60)
				return  -1;
			sum += 2 * i;
			sleep (2 * i++);
		}
	}
	return 0;
}
static const char* cmdid_to_string (int status)
{
	switch (status) {
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_AP); //������ssid�Ļ�����������Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_AP2);                  //���ø�ssid�Ļ�����������Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_CLOSE);                   //�ر�wifi����Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_OPEN);                    //����wifi����Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_FORCE_RESTART);                 //����wifi����Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_ADVANCED);                //wifi�ĸ߼�����������Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_WPS);                     //����WPS���ܣ�2�����Զ��أ��ʲ���Ҫ�ر���Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_WPS_UP);                  //WPSģ�鷢����WPS��������Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_WPS_DOWN);                //WPSģ�鷢����WPS �رյ���Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_SLEEP);                   //WEBUI����˯��ʱ������Ϣ
		CASE_RETURN_STR (MSG_CMD_CAPTURE_STATION);              //��ѯsta���������
		CASE_RETURN_STR (MSG_CMD_WIFI_COVERAGE);                //����wifi�ĸ��Ƿ�Χ
		CASE_RETURN_STR (MSG_CMD_WIFI_MAC);                     //����mac���˹���
		CASE_RETURN_STR (MSG_CMD_WIFI_CHANNEL_FOLLOW);          //����wifi AP�ŵ�����STA
		CASE_RETURN_STR (MSG_CMD_USB_CHARGING_CHANGED);         //������⵽usb���״̬�仯�ϱ���Ϣ
		//for apstation
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_OPEN);            //��apsta����
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_CLOSE);           //�ر�apsta����
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_CLOSE_OPEN_MSSID); //��apsta���رն�ssid
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_SCAN);            //ɨ���ȵ�
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_CONNECT);         //�����ȵ�
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_CONNECT_TIMEOUT); //���ӳ�ʱ�Ĵ�����Ϣ

		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_SIM_INIT_TIMER_PROCESS);        //eap-sim/aka���ܣ���ѯsim���������õĶ�ʱ����Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_GET_SCAN_RESULTS); // wpa_supplicant����ɨ��8sһ��
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_DISCONNECT);                    //�����Ͽ��������ӵ��ȵ�
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_SPOT_UPDATE);                   //�����ȵ��б�
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_EVENT_CONNECTING);            //wpa_supplicant���ڳ��������¼���Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_EVENT_CONNECTED);               //apsta���ȵ����ӳɹ�֪ͨ��Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_EVENT_DISCONNECTED);            //apsta���ȵ�Ͽ�����֪ͨ��Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_BLC_CONNECTED);               //blc���͵����ӳɹ���station��ip�ѷ��䵽
		CASE_RETURN_STR (MSG_CMD_BLC_OPEN_WIFISTATION_ACCESS);                //blc������Ϣ��apsta����
		CASE_RETURN_STR (MSG_CMD_BLC_CLOSE_WIFISTATION_ACCESS);               //blc������Ϣ�Ͽ�apsta����
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_NO_AVAILABLE_AP);
//	CASE_RETURN_STR(MSG_CMD_WIFI_STATION_PROCESS_TRYING_ASSOC);
		CASE_RETURN_STR(MSG_CMD_WIFI_STATION_TEMP_DISABLED);
		CASE_RETURN_STR(MSG_CMD_WIFI_STATION_FAILED_SET);

		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_END);                           //apstation����Ϣ��Χend
		//timing of sleep wake up
		CASE_RETURN_STR (MSG_CMD_WIFI_TSW_SET);        //���ö�ʱ˯�߻��ѵ���Ϣ
		CASE_RETURN_STR (MSG_CMD_WIFI_TSW_CLOSE);      //�رն�ʱ˯�߻��ѹ��ܵ���Ϣ
		CASE_RETURN_STR (MSG_CMD_RTC_TIME_UPDATE);     //rtcʱ�� �������������ö�ʱ˯�߻���
		CASE_RETURN_STR (MSG_CMD_WIFI_RESET_TIMER);    //mmi������������˯��timer
		CASE_RETURN_STR (RTC_MSG_ALARM_ELAPSED);    //mmi������������˯��timer
		CASE_RETURN_STR (MSG_CMD_STA_COUNT_CHANGE);
		CASE_RETURN_STR (MSG_CMD_CSIM_AUTH_RSP);
		CASE_RETURN_STR (MSG_CMD_CARD_MODE_RSP);
		CASE_RETURN_STR (MSG_CMD_PIN_STATUS_RSP);
		CASE_RETURN_STR (MSG_CMD_PIN_VERIFY_RSP);
		CASE_RETURN_STR (MSG_CMD_PIN_NUM_RSP);
		CASE_RETURN_STR (MSG_CMD_CRSM_RSP);
		CASE_RETURN_STR (MSG_CMD_CIMI_RSP);
		CASE_RETURN_STR (MSG_CMD_WIFIKEY_SET_RSP);
		CASE_RETURN_STR (MSG_CMD_NET_WAN_DIAL_FAIL);
		CASE_RETURN_STR (MSG_CMD_WIFI_SWITCH_REQ);
		CASE_RETURN_STR (MSG_CMD_WIFI_SWITCH_RSP);
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_SSID_REQ);
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_SSID_RSP);
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_PWD_REQ);
		CASE_RETURN_STR (MSG_CMD_WIFI_CFG_PWD_RSP);
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_ON_REQ);				//��WiFi����
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_ON_RSP);				//��WiFi��Ӧ
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_OFF_REQ);				//�ر�WiFi����
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_OFF_RSP);				//�ر�WiFi��Ӧ
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_SECURITY_REQ);			//����WiFi��ȫģʽ��SSID����������
		CASE_RETURN_STR (MSG_CMD_WIFI_SET_SECURITY_RSP);			//����WiFi��ȫģʽ��SSID�������Ӧ

		CASE_RETURN_STR (MSG_CMD_WIFI_GET_STATE_REQ); 			//��ȡWiFi״̬(����)����
		CASE_RETURN_STR (MSG_CMD_WIFI_GET_STATE_RSP); 			//��ȡWiFi״̬(����)��Ӧ
		CASE_RETURN_STR (MSG_CMD_WIFI_GET_INFO_REQ);				//��ȡWiFi��Ϣ(SSID�����롢��ȫģʽ)����
		CASE_RETURN_STR (MSG_CMD_WIFI_GET_INFO_RSP);				//��ȡWiFi��Ϣ(SSID�����롢��ȫģʽ)��Ӧ
		CASE_RETURN_STR (MSG_CMD_WIFI_GET_USER_LIST_REQ); 		//��ȡWiFi�û��б�����
		CASE_RETURN_STR (MSG_CMD_WIFI_GET_USER_LIST_RSP); 		//��ȡWiFi�û��б���Ӧ
		CASE_RETURN_STR (MSG_CMD_WIFI_NOTIFY_CHARGER_STATUS);
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_SUPPLICANT_EVT);		
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_INDICATE_SCAN_RESULTS);
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_FORGET);
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_DEBUG_DUMP);
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_WPA_CMD);
		CASE_RETURN_STR (MSG_CMD_AP_STA_CONNECTED);
		CASE_RETURN_STR (MSG_CMD_AP_STA_DISCONNECTED);
		CASE_RETURN_STR (MSG_CMD_WIFI_STATION_CONNECTED_ENABLE_AP_CMD);

		CASE_RETURN_STR (MSG_CMD_WIFI_TESTMODE_REQ);
		CASE_RETURN_STR (MSG_CMD_WIFI_TESTMODE_RSP);
	};
	return "UNKNOWN  CMD ID";
}

static const char* modid_to_string (int status)
{
	switch (status) {
		CASE_RETURN_STR (MODULE_ID_WEB_CGI);           /* 0x1001  WEB CGIģ�����Ϣ����ID*/
		CASE_RETURN_STR (MODULE_ID_WIFI);              /*0x 1003 wifi control*/
		CASE_RETURN_STR (MODULE_ID_WLAN_SERVER);       /* 0x1004 wifi�Լ����Լ�����Ϣ�����msg id*/
		CASE_RETURN_STR (MODULE_ID_MMI);               //0x 1005 add by wuhong for ufi MMI
		CASE_RETURN_STR (MODULE_ID_RTC_SERVICE);  //0x1007
		CASE_RETURN_STR (MODULE_ID_AT_CTL);    			/*0x1021 at ctlģ��ID*/
		CASE_RETURN_STR (MODULE_ID_MAIN_CTRL);         /*0x1028 ����ģ��ID*/
		CASE_RETURN_STR (MODULE_ID_ZSSID);             /*0x1029 zte_ssid added by zhangnan00123908 20131121*/
		CASE_RETURN_STR (MODULE_ID_WPA_SUPPLICANT);  //0x102D
		CASE_RETURN_STR (MODULE_ID_ZCORE);  //0x2003
		CASE_RETURN_STR (MODULE_ID_MMI_SVR);  //0x2003
	}
	return "UNKNOWN  Module ID";
}

/**************************************************************************
* description of function:  wlan deal queue
* input parameter: pstMsg:message content
* return value: 0:success -1:error
* zhaoyong set up this function
**************************************************************************/
int wifi_parse_msg (MSG_BUF *pMsg)
{
	int ret = -1;
	wf_log ("src_mod=[0x%x]%s, cmd=[0x%x]%s", pMsg->src_id, modid_to_string (pMsg->src_id),
	        pMsg->usMsgCmd, cmdid_to_string (pMsg->usMsgCmd));

	if (pMsg->ulMagic != MSG_MAGIC_WORD) {
		wf_log ("pstMsg magic error");
		return -1;
	} else if (pMsg->dst_id != MODULE_ID_WIFI) {
		wf_log ("target is not wlan pstMsg->dst_id is %x", pMsg->dst_id);
		return -1;
	}

	if (g_wifi_testmode == 1 &&
	   pMsg->usMsgCmd != MSG_CMD_WIFI_TESTMODE_REQ &&
	   pMsg->usMsgCmd != MSG_CMD_AP_STA_DISCONNECTED &&
	   pMsg->usMsgCmd != MSG_CMD_STA_COUNT_CHANGE) {//notify
		wf_log ("wifitest is running, discard normal cfg message!!");
		return -1;
	}

#if (PRODUCT_TYPE == PRODUCT_PHONE)//kw3 
	if(pMsg->usMsgCmd != MSG_CMD_WIFI_RESET_TIMER &&
	   pMsg->usMsgCmd != MSG_CMD_USB_CHARGING_CHANGED &&
	   pMsg->usMsgCmd != MSG_CMD_WIFI_NOTIFY_CHARGER_STATUS )
		check_wifi_mac();
#else
	if(pMsg->usMsgCmd != MSG_CMD_WIFI_RESET_TIMER &&
	   pMsg->usMsgCmd != MSG_CMD_USB_CHARGING_CHANGED)
		check_wifi_mac();

#endif

#ifdef __STA_FUNC__
	ret = wlan_sta_parse_msg(pMsg);
#endif

#ifdef __AP_FUNC__
#ifdef __STA_FUNC__ //kw 3
	if(-1 == ret)// sta not deal the msg
#endif
		wlan_ap_msg_handle (pMsg);
#endif

	if(pMsg->usMsgCmd != MSG_CMD_WIFI_RESET_TIMER && 
		pMsg->usMsgCmd != MSG_CMD_USB_CHARGING_CHANGED &&
		pMsg->usMsgCmd != MSG_CMD_AP_STA_CONNECTED &&
		pMsg->usMsgCmd != MSG_CMD_STA_COUNT_CHANGE &&
		pMsg->usMsgCmd != MSG_CMD_AP_STA_DISCONNECTED &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_WPS_DOWN &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_GET_SCAN_RESULTS &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_NO_AVAILABLE_AP &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_CONNECTED_ENABLE_AP_CMD &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_EVENT_CONNECTED &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_EVENT_DISCONNECTED &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_EVENT_CONNECTING &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_TEMP_DISABLED &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_FAILED_SET &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_CONNECT_TIMEOUT &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_STATION_BLC_CONNECTED &&
		pMsg->usMsgCmd != MSG_CMD_BLC_CLOSE_WIFISTATION_ACCESS &&
		pMsg->usMsgCmd != MSG_CMD_BLC_OPEN_WIFISTATION_ACCESS &&
		pMsg->usMsgCmd != MSG_CMD_WIFI_TESTMODE_REQ)
		cfg_save();
	return 0;
}



/*sig handler*/
static void sig_handler(int sig)
{
	wf_log("receive signal %d\n", sig);

    if((sig == SIGINT) || (sig == SIGTERM)) {
        wf_log("receive signal %d\n", sig);
        exit(0);
    }
	else if(sig == SIGUSR1)
	{
/**
@@@@	deal the wifi sleep problem
1. not sleep
2. delay the sleep
3. sleep adhead of time
**/
		wf_log("receive signal SIGUSR1 %d\n", sig);

	}
}


static void sig_init(void)
{
    pthread_t tid = pthread_self();
    wf_log("tid:%lu", tid);
    struct sigaction sigact;
    int ret;
    sigset_t signal_mask;
    sigemptyset(&signal_mask);
    sigaddset(&signal_mask, SIGPIPE);
    ret = pthread_sigmask(SIG_BLOCK, &signal_mask, NULL);
    if(ret != 0) {
        wf_log("block SIGPIPE error\n");
    }

    sigact.sa_handler = sig_handler;
    sigact.sa_flags = 0;
    sigemptyset(&sigact.sa_mask);
    sigaction(SIGINT, &sigact, NULL);
    sigaction(SIGTERM, &sigact, NULL);
    sigaction(SIGUSR1, &sigact, NULL);

    /*ignore SIGPIPE*/
    sigact.sa_handler = SIG_IGN;
    sigaction(SIGPIPE, &sigact, NULL);

}


void check_wifi_mac()
{
	char  wifi_mac[20] = {0};
	int   i = 0;
	
	cfg_get_item("wifi_mac", wifi_mac, sizeof(wifi_mac));

	if(strlen(wifi_mac)==0){
		for(i=0; i < 50; i++){
			wf_log ("############################################################");
			wf_log ("####  Please set a globally unique Valid MAC by UeTester                 ");
			wf_log ("####   watch:   at+mac=xxxxxxxxxxxx,                                                ");
			wf_log ("####   mifi:   at+mac=xxxxxxxxxxxx,    at+mac2=xxxxxxxxxxxx            ");
			wf_log ("####   MAC address length  is 12 bytes, can not be all of 0 or  F         ");
			wf_log ("####   MAC address can not be a broadcast address                           ");
			wf_log ("############################################################\n");
			sleep (6);
		}
		softap_assert("Please set a globally unique Valid MAC by UeTester,  at+mac=xxxxxxxxxxxx!!!");
	}
}

/* Started by AICoder, pid:cc62aedad4800f1140a60bb270857913a4f5992a */
void set_band_mode(void)
{
#ifdef WIFI_UNCOEXIST_5G

    char wifi_band[8] = {0};
    cfg_get_item("wifi_band", wifi_band, sizeof(wifi_band));

    const char* WIFI_BAND_A = "a";
    switch (strcmp(wifi_band, WIFI_BAND_A)) {
        case 0:
            g_work_mode = IN_WIFI_WORK_MODE_AP1;
            break;
        default:
            g_work_mode = IN_WIFI_WORK_MODE_AP0;
            break;
    }
#endif
}
/* Ended by AICoder, pid:cc62aedad4800f1140a60bb270857913a4f5992a */

void init_config()
{
	cfg_set ("WscModeOption", "0");
	cfg_set ("wifiSleep", "0");
	cfg_set ("wifi_root_dir", "/etc_rw");
	cfg_set ("sta_count", "0");
	cfg_set ("m_sta_count", "0");
	cfg_set ("sleep_debug", "0");
	//cfg_set("sta_ip_status", "disconnect");
	//cfg_set("scan_finish", "0");
	//cfg_set("EX_APLIST", "");
	//cfg_set("EX_APLIST1", "");
	//cfg_set("manual_d_wifi", "0");
	//cfg_set("EX_SSID1", "");
	//cfg_set("EX_AuthMode", "");
	//cfg_set("EX_EncrypType", "");
	//cfg_set("EX_DefaultKeyID", "");
	//cfg_set("EX_WEPKEY", "");
	//cfg_set("EX_WPAPSK1", "");
	//cfg_set("EX_wifi_profile", "");
	//cfg_set("EX_mac", "");

	cfg_set ("wpa_supplicant", "stop");
	cfg_set ("wifi_cur_state", WIFI_CLOSED);
#if (PRODUCT_TYPE == PRODUCT_MIFI_CPE)
	cfg_set ("wifiEnabled", "1");
#elif (PRODUCT_TYPE == PRODUCT_PHONE)
	cfg_set ("wifiEnabled", "0");//yaoyuan special need:everytime system wakes up, wifi is off
#else
	cfg_set ("wifiEnabled", "0");
#endif

	ensure_config_dir_exist(WIFI_DIR);

#ifdef WIFI_UNCOEXIST_5G
	set_band_mode();
#endif
}


int wlan_main (int argc, char *argv[])
{
	int wlan_msg_queue_id = 0;
	int iRet = -1;
	MSG_BUF wlanMsg={0};
	int threadid = 0;
	char wifiEnabled[8] = {0};
	prctl(PR_SET_NAME, "wlan", 0, 0, 0);
//the first step,  log  and envirenment init
	loglevel_init();
	wf_log ("wlan-server start, build time:[%s %s]", __DATE__,__TIME__);
	sig_init();

#ifdef __AP_FUNC__
#ifdef __USE_AES__
	wifi_aes_init_key();
	if (2 == wifi_decrypt_code())
		wifi_encrypt_code();
#else
	wifi_decode_b2s();
#endif	
#endif

#if !(defined(__ESP_8089_CHIP__))
	send_get_mac_req();
#endif
	
//second step, nv init, ssid and wifi_mac init
	//create main thread msg queue id
	wlan_msg_queue_id = wf_create_msg_qid (MODULE_ID_WIFI);
#ifdef __STA_FUNC__	
	sem_init (&g_wpa_supplicant_id, 0 , 0);
#endif
#ifdef __AP_FUNC__
	#ifndef __REALTEK_8192_CHIP__
	sem_init (&g_hostap_id, 0 , 0);
	#endif
#endif


	init_config();
#if !(defined(__ESP_8089_CHIP__))
	get_mac_config_ssid_key_nv();
#endif

#ifdef __AP_FUNC__	
#if  defined( __RDA_5995_CHIP__)
	ap_server = 	&rda5995_ap;
#elif defined(__ESP_8089_CHIP__)
	ap_server = 	&esp8089_ap;
#elif defined(__REALTEK_8192_CHIP__)
	ap_server = 	&realtek_8192_ap;
#elif defined(__XR_819_CHIP__)
	ap_server = 	&xr819_ap;
#elif defined(__SSV_6X5X_CHIP__)
	ap_server = 	&ssv6x5x_ap;
#elif defined(__AIC_8800DW_CHIP__)
	ap_server = 	&aic8800dw_ap;
#endif
	/*insmod, and , start hostapd*/
	ap_server->init(ap_server);
#endif

#ifdef __STA_FUNC__
#if  defined( __RDA_5995_CHIP__)
	sta_manager = 	&rda5995_sta;
#elif defined(__ESP_8089_CHIP__)
	sta_manager = 	&esp8089_sta;
#elif defined(__REALTEK_8192_CHIP__)
	sta_manager = 	&realtek_sta;
#elif defined(__SSV_6X5X_CHIP__)
	sta_manager = 	&ssv6x5x_sta;
#elif defined(__AIC_8800DW_CHIP__)
	sta_manager = 	&aic8800dw_sta;
#endif
	sta_manager->init(sta_manager);
#endif

	if (check_wlan() == -1) {
		wf_log (" wlan0 interface is not exist ,please contact wlan driver engineer");
		cfg_set("wifi_chip", "0");
		cfg_save();
		return -1;
	}

#if defined(__ESP_8089_CHIP__)
	wifi_fw_mac_config_ssid(ap_server);
#endif

	wf_log ("wifichip exist, set  nv to tell mmi");
	cfg_set("wifi_chip", "1");
	


	#ifdef  __STA_FUNC__
	wlan_statemachine_init();
	#endif


//thrid step,  according wifiEnabled, to open wifi

	cfg_get_item ("wifiEnabled", wifiEnabled, sizeof (wifiEnabled));
	if (!strcmp (wifiEnabled, "1")) {
#ifdef __AP_FUNC__
		wlan_ap_open();
#endif

#ifdef __STA_FUNC__
		wlan_station_open();
#endif
	}
	else{
		wf_log ("rda5995 to rmmod");
#if  defined( __RDA_5995_CHIP__)
		sta_manager->drv_proxy.drv_deinit(&sta_manager->drv_proxy);
#endif		
	}

#ifdef __AP_FUNC__		
	qrcode_make();	
	set_globle_charging_status();
	wlan_prepare_sleep();
	handle_tsw_setting();				//���ö�ʱ˯�߻���
#endif


#if  defined  ( __RDA5995_WIFI_CHIP__) && (PRODUCT_TYPE == PRODUCT_PHONE)	
	wf_msg_to_zcore(MSG_CMD_WIFI_IS_READY, 0, NULL);  //wifi������֪ͨ�̶�̨��ʾwifi״̬
#endif

	//wf_create_thread ("netlink", netlink_loop);
	hotplug_parse_register(DEVICE_TYPE_APP_WIFI, wifi_app_msg_parse);

#ifdef  __STA_FUNC__
	wf_create_thread ("wlan-station", station_loop);
#endif

#ifdef __AP_FUNC__	
#ifndef __REALTEK_8192_CHIP__
	wf_create_thread ("hostap", hostap_loop);
#if defined(__MULTI_AP__)
	wf_log ("hostap_multi thread create");
	wf_create_thread ("hostap_multi", hostap_loop_multi);
#endif

#endif
#endif

	wf_log (" main thread getpid=%d", getpid());

	//prctl(PR_SET_NAME, "wlan-main", 0, 0, 0);
	while (1) {
		memset (&wlanMsg, 0, sizeof (MSG_BUF));
		iRet = msgrcv (wlan_msg_queue_id, &wlanMsg, sizeof (MSG_BUF) - sizeof (LONG), 0, 0);
		if (iRet == -1) {
			continue;
		}

		wifi_parse_msg (&wlanMsg);
	}
	return 0;
}

#ifdef ZXIC_ONELINK_TEST
/**
 * 清理指定MAC地址相关的所有iptables规则
 * @param mac MAC地址字符串
 */
static void cleanup_mac_iptables_rules(const char *mac)
{
	char cmd[256];
	int max_attempts = 10; // 最多尝试10次，避免无限循环
	int i;

	if (!mac || strlen(mac) == 0) {
		wf_log("cleanup_mac_iptables_rules: invalid MAC address");
		return;
	}

	wf_log("cleanup_mac_iptables_rules: cleaning up rules for MAC %s", mac);

	// 删除IPv4 iptables中该MAC的DNS允许规则（尝试删除多次直到没有）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D FORWARD -m mac --mac-source %s -p udp --dport 53 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break; // 删除失败，说明规则不存在，退出循环
		}
	}

	// 删除IPv4 iptables中该MAC的HTTP允许规则（INPUT链）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D INPUT -m mac --mac-source %s -p tcp --dport 80 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	// 删除IPv4 iptables中该MAC的HTTPS允许规则（INPUT链）
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D INPUT -m mac --mac-source %s -p tcp --dport 443 -j ACCEPT 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	// 删除IPv4 iptables中该MAC的DROP规则
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"iptables -D FORWARD -m mac --mac-source %s -j DROP 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break; // 没有找到规则，退出循环
		}
	}

	// 删除IPv6 ip6tables中该MAC的DROP规则
	for (i = 0; i < max_attempts; i++) {
		snprintf(cmd, sizeof(cmd),
			"ip6tables -D FORWARD -m mac --mac-source %s -j DROP 2>/dev/null", mac);
		if (system(cmd) != 0) {
			break;
		}
	}

	wf_log("cleanup_mac_iptables_rules: completed cleanup for MAC %s", mac);
}
#endif

